{"doc": "\n 转义和反转义工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "escape", "paramTypes": ["java.lang.String"], "doc": "\n 转义文本中的HTML字符为安全的字符\r\n\r\n @param text 被转义的文本\r\n @return 转义后的文本\r\n"}, {"name": "unescape", "paramTypes": ["java.lang.String"], "doc": "\n 还原被转义的HTML特殊字符\r\n\r\n @param content 包含转义符的HTML内容\r\n @return 转换后的字符串\r\n"}, {"name": "clean", "paramTypes": ["java.lang.String"], "doc": "\n 清除所有HTML标签，但是不删除标签内的内容\r\n\r\n @param content 文本\r\n @return 清除标签后的文本\r\n"}, {"name": "encode", "paramTypes": ["java.lang.String"], "doc": "\n Escape编码\r\n\r\n @param text 被编码的文本\r\n @return 编码后的字符\r\n"}, {"name": "decode", "paramTypes": ["java.lang.String"], "doc": "\n Escape解码\r\n\r\n @param content 被转义的内容\r\n @return 解码后的字符串\r\n"}], "constructors": []}
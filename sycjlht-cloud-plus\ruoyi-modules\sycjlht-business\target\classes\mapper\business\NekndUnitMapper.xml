<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndUnitMapper">
    
    <resultMap type="NekndUnit" id="NekndUnitResult">
        <result property="id"    column="id"    />
        <result property="unitType"    column="unit_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="unitUri"    column="unit_uri"    />
        <result property="unitName"    column="unit_name"    />
        <result property="synopsis"    column="synopsis"    />
        <result property="content"    column="content"    />
        <result property="propertyClass"    column="property_class"    />
        <result property="registrationType"    column="registration_type"    />
        <result property="scale"    column="scale"    />
        <result property="workers"    column="workers"    />
        <result property="industry"    column="industry"    />
        <result property="timeNode"    column="time_node"    />
        <result property="address"    column="address"    />
        <result property="sourceUri"    column="source_uri"    />
        <result property="schoolType"    column="school_type"    />
        <result property="floorArea"    column="floor_area"    />
        <result property="internalStudents"    column="internal_students"    />
        <result property="isClassic"    column="is_classic"    />
        <result property="schoolLevels"    column="school_levels"    />
        <result property="isSuperior"    column="is_superior"    />
        <result property="isSuperiors"    column="is_superiors"    />
        <result property="organizerType"    column="organizer_type"    />
        <result property="sponsorLevel"    column="sponsor_level"    />
        <result property="administrativeLevel"    column="administrative_level"    />
        <result property="unitNature"    column="unit_nature"    />
        <result property="responsibility"    column="responsibility"    />
        <result property="functionInfo"    column="function_info"    />
        <result property="belongPark"    column="belong_park"    />
        <result property="provincialId"    column="provincial_id"    />
        <result property="provincialName"    column="provincial_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
    </resultMap>

    <sql id="selectNekndUnitVo">
        select id, unit_type, del_flag, create_time, update_time, unit_uri, unit_name, synopsis, content, property_class, registration_type, scale, workers, industry, time_node, address, source_uri, school_type, floor_area, internal_students, is_classic, school_levels, is_superior, is_superiors, organizer_type, sponsor_level, administrative_level, unit_nature, responsibility, function_info,belong_park,
            provincial_id,  provincial_name, city_id, city_name  from neknd_unit
    </sql>

    <select id="selectNekndUnitList" parameterType="NekndUnit" resultMap="NekndUnitResult">
        <include refid="selectNekndUnitVo"/>
        <where>  
            <if test="unitType != null  and unitType != ''"> and unit_type = #{unitType}</if>
            <if test="unitUri != null  and unitUri != ''"> and unit_uri = #{unitUri}</if>
            <if test="unitName != null  and unitName != ''"> and unit_name like concat('%', #{unitName}, '%')</if>
            <if test="synopsis != null  and synopsis != ''"> and synopsis = #{synopsis}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="propertyClass != null  and propertyClass != ''"> and property_class = #{propertyClass}</if>
            <if test="registrationType != null  and registrationType != ''"> and registration_type = #{registrationType}</if>
            <if test="scale != null  and scale != ''"> and scale = #{scale}</if>
            <if test="workers != null  and workers != ''"> and workers = #{workers}</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="timeNode != null  and timeNode != ''"> and time_node = #{timeNode}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="sourceUri != null  and sourceUri != ''"> and source_uri = #{sourceUri}</if>
            <if test="schoolType != null  and schoolType != ''"> and school_type = #{schoolType}</if>
            <if test="floorArea != null  and floorArea != ''"> and floor_area = #{floorArea}</if>
            <if test="internalStudents != null  and internalStudents != ''"> and internal_students = #{internalStudents}</if>
            <if test="isClassic != null  and isClassic != ''"> and is_classic = #{isClassic}</if>
            <if test="schoolLevels != null  and schoolLevels != ''"> and school_levels = #{schoolLevels}</if>
            <if test="isSuperior != null  and isSuperior != ''"> and is_superior = #{isSuperior}</if>
            <if test="isSuperiors != null  and isSuperiors != ''"> and is_superiors = #{isSuperiors}</if>
            <if test="organizerType != null  and organizerType != ''"> and organizer_type = #{organizerType}</if>
            <if test="sponsorLevel != null  and sponsorLevel != ''"> and sponsor_level = #{sponsorLevel}</if>
             <if test="administrativeLevel != null  and administrativeLevel != ''"> and administrative_level = #{administrativeLevel}</if>
              <if test="unitNature != null  and unitNature != ''"> and unit_nature = #{unitNature}</if>
               <if test="responsibility != null  and responsibility != ''"> and responsibility = #{responsibility}</if>
                 <if test="functionInfo != null  and functionInfo != ''"> and function_info = #{functionInfo}</if>
                 <if test="belongPark != null  and belongPark != ''"> and belong_park = #{belongPark}</if>
                 <if test="provincialId != null  and provincialId != ''"> and provincial_id = #{provincialId}</if>
                 <if test="provincialName != null  and provincialName != ''"> and provincial_name = #{provincialName}</if>
                 <if test="cityId != null  and cityId != ''"> and city_id = #{cityId} </if>
                 <if test="cityName != null  and cityName != ''"> and city_name = #{cityName} </if>
        </where>
    </select>
    
    <select id="selectNekndUnitById" parameterType="Long" resultMap="NekndUnitResult">
        <include refid="selectNekndUnitVo"/>
        where id = #{id}
    </select>

    <resultMap id="parkDictMap" type="hashmap">
        <result property="id" column="id" />
        <result property="unitName" column="unit_name" />
    </resultMap>
    <select id="getParkDict" resultMap="parkDictMap">
        select id, unit_name from neknd_unit where unit_type like '%3%'
    </select>

    <resultMap id="leadingEnterpriseDataMap" type="hashmap">
        <result property="unitName" column="unit_name" />
        <result property="staffEducationRatio" column="staff_education_ratio" />
        <result property="skillTrainingRatio" column="skill_training_ratio" />
        <result property="schoolUndertakeRatio" column="school_undertake_ratio" />
        <result property="compatibilityRatio" column="compatibility_ratio" />
        <result property="provincialId" column="provincial_id" />
        <result property="cityId" column="city_id" />
    </resultMap>
    <select id="getLeadingEnterpriseData" resultMap="leadingEnterpriseDataMap">
        select u.unit_name,u.provincial_id,u.city_id, p.staff_education_ratio,p.skill_training_ratio,p.school_undertake_ratio,p.compatibility_ratio
        from neknd_unit u left join neknd_unit_park p on u.id = p.uid where u.unit_type like '%3%'
    </select>
    <select id="getParkDictByCIdAndPId" resultMap="parkDictMap">
        select id, unit_name from neknd_unit where unit_type like '%3%' <if test="CId != null">and city_id = #{CId}</if> <if test="PId != null">and provincial_id = #{PId}</if>
    </select>


    <resultMap id="substantiveIndicatorStatisticsDataMap" type="hashmap">
        <result property="unitName" column="unit_name"/>
        <result property="parkWorkAssessmentNumber" column="park_work_assessment_number"/>
        <result property="vocationalEducationAssessmentNumber" column="vocational_education_assessment_number"/>
        <result property="amountInPlace" column="amount_in_place"/>
        <result property="bylaw" column="bylaw"/>
        <result property="deliberationRules" column="deliberation_rules"/>
        <result property="responsibilitiesDivisionDocument" column="responsibilities_division_document"/>
        <result property="specialFundPlaning" column="special_fund_planing"/>
        <result property="dailyExpensePlaning" column="daily_expense_planing"/>
        <result property="localEmploymentRate" column="local_employment_rate"/>
        <result property="isAdjustmentEstablish" column="is_adjustment_establish"/>
        <result property="employmentMatchingRate" column="employment_matching_rate"/>
        <result property="exemptedEnterprisesCount" column="exempted_enterprises_count"/>
        <result property="landConcessionsCompaniesNumber" column="land_concessions_companies_number"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="cityId" column="city_id"/>
        <collection property="memberUnits" javaType="collection" ofType="hashmap">
            <result property="belong_park" column="belong_park"/>
            <result property="unitType" column="unit_type"/>
            <result property="count" column="count"/>
        </collection>
    </resultMap>

    <select id="getSubstantiveIndicatorStatisticsData" resultMap="substantiveIndicatorStatisticsDataMap">
        SELECT u.unit_name,u.provincial_id,u.city_id, c.belong_park, c.unit_type, c.count,p.park_work_assessment_number,p.vocational_education_assessment_number,
               p.amount_in_place ,p.bylaw,p.deliberation_rules,p.responsibilities_division_document,p.special_fund_planing,p.daily_expense_planing,
               p.local_employment_rate ,p.is_adjustment_establish,p.employment_matching_rate,p.exempted_enterprises_count,p.land_concessions_companies_number
        FROM neknd_unit u LEFT JOIN neknd_unit_park p on u.id=p.uid
            LEFT JOIN ( SELECT a.belong_park, a.unit_type, COUNT(a.belong_park) AS count FROM neknd_unit a WHERE a.belong_park IS NOT NULL GROUP BY a.belong_park, a.unit_type ) c ON u.id = c.belong_park
        WHERE u.unit_type LIKE '%3%'
    </select>

    <insert id="insertNekndUnit" parameterType="NekndUnit" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="unitType != null and unitType != ''">unit_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="unitUri != null and unitUri != ''">unit_uri,</if>
            <if test="unitName != null and unitName != ''">unit_name,</if>
            <if test="synopsis != null and synopsis != ''">synopsis,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="propertyClass != null">property_class,</if>
            <if test="registrationType != null">registration_type,</if>
            <if test="scale != null">scale,</if>
            <if test="workers != null">workers,</if>
            <if test="industry != null and industry != ''">industry,</if>
            <if test="timeNode != null and timeNode != ''">time_node,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="sourceUri != null">source_uri,</if>
            <if test="schoolType != null">school_type,</if>
            <if test="floorArea != null">floor_area,</if>
            <if test="internalStudents != null">internal_students,</if>
            <if test="isClassic != null">is_classic,</if>
            <if test="schoolLevels != null">school_levels,</if>
            <if test="isSuperior != null">is_superior,</if>
            <if test="isSuperiors != null">is_superiors,</if>
            <if test="organizerType != null">organizer_type,</if>
            <if test="sponsorLevel != null">sponsor_level,</if>
            <if test="administrativeLevel != null">administrative_level,</if>
            <if test="unitNature != null">unit_nature,</if>
            <if test="responsibility != null">responsibility,</if>
            <if test="functionInfo != null">function_info,</if>
            <if test="belongPark != null">belong_park,</if>
            <if test="provincialId != null">provincial_id,</if>
            <if test="provincialName != null">provincial_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="unitType != null and unitType != ''">#{unitType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="unitUri != null and unitUri != ''">#{unitUri},</if>
            <if test="unitName != null and unitName != ''">#{unitName},</if>
            <if test="synopsis != null and synopsis != ''">#{synopsis},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="propertyClass != null">#{propertyClass},</if>
            <if test="registrationType != null">#{registrationType},</if>
            <if test="scale != null">#{scale},</if>
            <if test="workers != null">#{workers},</if>
            <if test="industry != null and industry != ''">#{industry},</if>
            <if test="timeNode != null and timeNode != ''">#{timeNode},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="sourceUri != null">#{sourceUri},</if>
            <if test="schoolType != null">#{schoolType},</if>
            <if test="floorArea != null">#{floorArea},</if>
            <if test="internalStudents != null">#{internalStudents},</if>
            <if test="isClassic != null">#{isClassic},</if>
            <if test="schoolLevels != null">#{schoolLevels},</if>
            <if test="isSuperior != null">#{isSuperior},</if>
            <if test="isSuperiors != null">#{isSuperiors},</if>
            <if test="organizerType != null">#{organizerType},</if>
            <if test="sponsorLevel != null">#{sponsorLevel},</if>
            <if test="administrativeLevel != null">#{administrativeLevel},</if>
            <if test="unitNature != null">#{unitNature},</if>
            <if test="responsibility != null">#{responsibility},</if>
            <if test="functionInfo != null">#{functionInfo},</if>
            <if test="belongPark != null">#{belongPark},</if>
            <if test="provincialId != null">#{provincialId},</if>
            <if test="provincialName != null">#{provincialName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
         </trim>
    </insert>

    <update id="updateNekndUnit" parameterType="NekndUnit">
        update neknd_unit
        <trim prefix="SET" suffixOverrides=",">
            <if test="unitType != null and unitType != ''">unit_type = #{unitType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="unitUri != null and unitUri != ''">unit_uri = #{unitUri},</if>
            <if test="unitName != null and unitName != ''">unit_name = #{unitName},</if>
            <if test="synopsis != null and synopsis != ''">synopsis = #{synopsis},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="propertyClass != null">property_class = #{propertyClass},</if>
            <if test="registrationType != null">registration_type = #{registrationType},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="workers != null">workers = #{workers},</if>
            <if test="industry != null and industry != ''">industry = #{industry},</if>
            <if test="timeNode != null and timeNode != ''">time_node = #{timeNode},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="sourceUri != null">source_uri = #{sourceUri},</if>
            <if test="schoolType != null">school_type = #{schoolType},</if>
            <if test="floorArea != null">floor_area = #{floorArea},</if>
            <if test="internalStudents != null">internal_students = #{internalStudents},</if>
            <if test="isClassic != null">is_classic = #{isClassic},</if>
            <if test="schoolLevels != null">school_levels = #{schoolLevels},</if>
            <if test="isSuperior != null">is_superior = #{isSuperior},</if>
            <if test="isSuperiors != null">is_superiors = #{isSuperiors},</if>
            <if test="organizerType != null">organizer_type = #{organizerType},</if>
            <if test="sponsorLevel != null">sponsor_level = #{sponsorLevel},</if>
            <if test="administrativeLevel != null">administrative_level = #{administrativeLevel},</if>
            <if test="unitNature != null">unit_nature = #{unitNature},</if>
            <if test="responsibility != null">responsibility = #{responsibility},</if>
            <if test="functionInfo != null">function_info = #{functionInfo},</if>
            <if test="belongPark != null">belong_park = #{belongPark},</if>
            <if test="provincialId != null">provincial_id = #{provincialId},</if>
            <if test="provincialName != null">provincial_name = #{provincialName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndUnitById" parameterType="Long">
        delete from neknd_unit where id = #{id}
    </delete>

    <delete id="deleteNekndUnitByIds" parameterType="String">
        delete from neknd_unit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap id="parkNames" type="hashmap">
        <result property="id" column="id"/>
        <result property="unitName" column="unit_name"/>
        <result property="provincialName" column="provincial_name"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="cityName" column="city_name"/>
        <result property="cityId" column="city_id"/>
    </resultMap>
    <select id="getParkName" parameterType="java.util.Set" resultMap="parkNames">
        select
            id,unit_name, provincial_id, provincial_name, city_id, city_name
        from
            neknd_unit
        where
            id in
        <foreach item="id" collection="keys" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <resultMap id="cityNames" type="hashmap">
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="provincialName" column="provincial_name"/>
    </resultMap>
    <select id="getCityName" parameterType="java.util.Set" resultMap="cityNames">
        select
        city_id, provincial_name, city_name
        from
        neknd_unit
        where
        city_id in
        <foreach item="id" collection="keys" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <resultMap id="provincialNames" type="hashmap">
        <result property="provincialId" column="provincial_id"/>
        <result property="cityName" column="city_name"/>
        <result property="provincialName" column="provincial_name"/>
    </resultMap>
    <select id="getProvincialName" parameterType="java.util.Set" resultMap="provincialNames">
        select
        provincial_id, provincial_name, city_name
        from
        neknd_unit
        where
        provincial_id in
        <foreach item="id" collection="keys" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <resultMap id="IndustrialParkCount" type="hashmap">
        <result property="id" column="id"/>
        <result property="provincialId" column="provincial_id"/>
        <result property="cityId" column="city_id"/>
        <result property="parkCount" column="parkCount"/>
        <result property="parkType" column="parkType"/>
        <result property="parkIndustrialOutput" column="parkIndustrialOutput"/>
        <result property="leadingIndustry" column="leadingIndustry"/>
    </resultMap>
    <select id="getIndustrialParkCount" resultMap="IndustrialParkCount">
        SELECT
            unit.id,
            unit.provincial_id,
            unit.city_id,
            COUNT(unit.id) AS parkCount,  -- 统计每个单位的园区数
            park.park_type as parkType,
            park.park_industrial_output as parkIndustrialOutput,
            park.leading_industry as leadingIndustry
        FROM neknd_unit as unit
        inner join neknd_unit_park as park
        on unit.id = park.uid
        WHERE unit_type = '3'
        GROUP BY id, provincial_id, city_id; -- 关键：按单位分组
    </select>

<!--    <resultMap id="ParkPolicyIndicatorMap" type="com.neknd.system.domain.NekndVisualizeCity">-->
    <resultMap id="ParkPolicyIndicatorMap" type="hashmap">
        <result property="financialPolicyRate" column="financial_policy_rate" />
        <result property="fiscalPolicyRate" column="fiscal_policy_rate" />
        <result property="landPolicyRate" column="land_policy_rate" />
        <result property="enterpriseTaxBenefit" column="enterprise_tax_benefit" />
        <result property="equipmentDonation" column="equipment_donation" javaType="java.lang.Long" />
        <result property="highSkillTalentCount" column="high_skill_talent_count" javaType="java.lang.Long"  />
        <result property="qualifiedOrderCount" column="qualified_order_count" javaType="java.lang.Long"  />
        <result property="dominantIndustryMatchRate" column="dominant_industry_match_rate" />
        <result property="newMajorsRequired" column="new_majors_required" />
        <result property="obsoleteMajorsRequired" column="obsolete_majors_required" />
        <result property="localEmploymentRate" column="local_employment_rate" />
        <result property="employmentMatchingRate" column="employment_matching_rate" />
        <result property="conversionFunds" column="conversion_funds" javaType="java.lang.Long" />
        <result property="parkIndustrialOutput" column="park_industrial_output" />
        <result property="parkOutputRatio" column="park_output_ratio" />
    </resultMap>
    <select id="getVisualizeDataOfPark" resultMap="ParkPolicyIndicatorMap">
        select
            round(avg(park.financial_policy_rate), 2) as financial_policy_rate,
            round(avg(park.fiscal_policy_rate), 2) as fiscal_policy_rate,
            round(avg(park.land_policy_rate), 2) as land_policy_rate,
            round(avg(park.enterprise_tax_benefit), 2) as enterprise_tax_benefit,
            sum(park.equipment_donation) as equipment_donation,
            sum(park.high_skill_talent_count) as high_skill_talent_count,
            sum(park.qualified_order_count) as qualified_order_count,
            JSON_ARRAYAGG(park.dominant_industry_match_rate) as dominant_industry_match_rate,
            TRIM(LEADING '、' FROM
                GROUP_CONCAT(
                    DISTINCT park.new_majors_required
                    ORDER BY park.new_majors_required
                    SEPARATOR '、'
            )) as new_majors_required,
            TRIM(LEADING '、' FROM GROUP_CONCAT(
                DISTINCT park.obsolete_majors_required
                ORDER BY park.obsolete_majors_required
                SEPARATOR '、'
            )) as obsolete_majors_required,
            JSON_ARRAYAGG(park.local_employment_rate) as local_employment_rate,
            JSON_ARRAYAGG(park.employment_matching_rate) as employment_matching_rate,
            sum(park.conversion_funds) as conversion_funds,
            JSON_ARRAYAGG(park.park_industrial_output) as park_industrial_output,
            JSON_ARRAYAGG(park.park_output_ratio) as park_output_ratio
        from
            neknd_unit as unit
        inner join
            neknd_unit_park as park
        on
            park.uid = unit.id
        <where>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId} </if>
            and unit.unit_type = '3'
        </where>
        group by NULL;
    </select>

<!--    <resultMap id="VocationalEducationStatsMap" type="com.neknd.system.domain.NekndVisualizeCity">-->
    <resultMap id="VocationalEducationStatsMap" type="hashmap">
        <result property="trainingBaseNumber" column="training_base_number" javaType="java.lang.Long" />
        <result property="inventionPatentCount" column="invention_patent_count" javaType="java.lang.Long" />
        <result property="frontLinePostsNumber" column="front_line_posts_number" javaType="java.lang.Long" />
        <result property="jointMajorCount" column="joint_major_count" javaType="java.lang.Long" />
        <result property="featuredMajorCount" column="featured_major_count" javaType="java.lang.Long" />
        <result property="partTimeClassesNumber" column="part_time_classes_number" javaType="java.lang.Long" />
        <result property="provideAnnualEmployees" column="provide_annual_employees" javaType="java.lang.Long" />
        <result property="coDevelopCoursesNumber" column="co_develop_courses_number" javaType="java.lang.Long" />
    </resultMap>
    <select id="getVisualizeDataOfProject" resultMap="VocationalEducationStatsMap">
        select
            sum(project.training_base_number) as training_base_number,
            sum(project.invention_patent_count) as invention_patent_count,
            sum(project.front_line_posts_number) as front_line_posts_number,
            sum(project.joint_major_count) as joint_major_count,
            sum(project.featured_major_count) as featured_major_count,
            sum(project.part_time_classes_number) as part_time_classes_number,
            sum(project.provide_annual_employees) as provide_annual_employees,
            sum(project.co_develop_courses_number) as co_develop_courses_number
        from
            neknd_unit as unit
        inner join
            neknd_industry_education_projects as project
        on
            unit.id = project.belonging_park
        <where>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId} </if>
            and unit.unit_type = '3'
        </where>
    </select>

    <resultMap id="participateData" type="hashmap">
        <result property="id" column="id"/>
        <result property="unitType" column="unit_type"/>
        <result property="unitCount" column="unit_count"/>
    </resultMap>
    <select id="getParticipateData" parameterType="String" resultMap="participateData">
        select
            nup.id,
            nu.unit_type,
            count(nu.unit_type) as unit_count
        from
            neknd_unit_park nup
        inner join
            neknd_unit nu on
            nup.uid = nu.id
        where
            nu.city_id = #{cityId}
        group by
            nu.city_id, nu.unit_type;
    </select>

    <select id="selectUnitNumbers" resultType="com.neknd.system.domain.NekndPolicyNewsUnitNumbers">
        select
            nu.provincial_id as pid,
            nu.city_id as cid,
            nu.id as park,
            count(unit.belong_park) as unitNumbers
        from
            neknd_unit nu
        inner join
            neknd_unit unit on
            unit.belong_park = nu.id
        group by
            unit.belong_park;
    </select>
</mapper>
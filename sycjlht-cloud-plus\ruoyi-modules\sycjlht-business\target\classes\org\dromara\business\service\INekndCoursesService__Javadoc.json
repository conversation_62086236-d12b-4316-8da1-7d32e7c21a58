{"doc": "\n 课程Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesByCourseId", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询课程\r\n\r\n @param courseId 课程主键\r\n @return 课程\r\n"}, {"name": "selectNekndCoursesList", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": "\n 查询课程列表\r\n\r\n @param nekndCourses 课程\r\n @return 课程集合\r\n"}, {"name": "insertNekndCourses", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": "\n 新增课程\r\n\r\n @param nekndCourses 课程\r\n @return 结果\r\n"}, {"name": "updateNekndCourses", "paramTypes": ["org.dromara.business.domain.NekndCourses"], "doc": "\n 修改课程\r\n\r\n @param nekndCourses 课程\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesByCourseIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除课程\r\n\r\n @param courseIds 需要删除的课程主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesByCourseId", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除课程信息\r\n\r\n @param courseId 课程主键\r\n @return 结果\r\n"}], "constructors": []}
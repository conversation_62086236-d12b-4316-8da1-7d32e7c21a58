{"doc": "\n Excel相关处理\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "sysDictMap", "doc": "\n 用于dictType属性数据存储，避免重复查缓存\r\n"}, {"name": "sheetSize", "doc": "\n Excel sheet最大行数，默认65536\r\n"}, {"name": "sheetName", "doc": "\n 工作表名称\r\n"}, {"name": "type", "doc": "\n 导出类型（EXPORT:导出数据；IMPORT：导入模板）\r\n"}, {"name": "wb", "doc": "\n 工作薄对象\r\n"}, {"name": "sheet", "doc": "\n 工作表对象\r\n"}, {"name": "styles", "doc": "\n 样式列表\r\n"}, {"name": "list", "doc": "\n 导入导出数据列表\r\n"}, {"name": "fields", "doc": "\n 注解列表\r\n"}, {"name": "rownum", "doc": "\n 当前行号\r\n"}, {"name": "title", "doc": "\n 标题\r\n"}, {"name": "maxHeight", "doc": "\n 最大高度\r\n"}, {"name": "subMergedLastRowNum", "doc": "\n 合并后最后行数\r\n"}, {"name": "subMergedFirstRowNum", "doc": "\n 合并后开始行数\r\n"}, {"name": "subMethod", "doc": "\n 对象的子列表方法\r\n"}, {"name": "subFields", "doc": "\n 对象的子列表属性\r\n"}, {"name": "statistics", "doc": "\n 统计列表\r\n"}, {"name": "DOUBLE_FORMAT", "doc": "\n 数字格式\r\n"}, {"name": "clazz", "doc": "\n 实体对象\r\n"}, {"name": "excludeFields", "doc": "\n 需要排除列属性\r\n"}], "enumConstants": [], "methods": [{"name": "hideColumn", "paramTypes": ["java.lang.String[]"], "doc": "\n 隐藏Excel中列属性\r\n\r\n @param fields 列属性名 示例[单个\"name\"/多个\"id\",\"name\"]\r\n @throws Exception\r\n"}, {"name": "createTitle", "paramTypes": [], "doc": "\n 创建excel第一行标题\r\n"}, {"name": "createSubHead", "paramTypes": [], "doc": "\n 创建对象的子列表名称\r\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream"], "doc": "\n 对excel表单默认第一个索引名转换成list\r\n\r\n @param is 输入流\r\n @return 转换后集合\r\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream", "int"], "doc": "\n 对excel表单默认第一个索引名转换成list\r\n\r\n @param is 输入流\r\n @param titleNum 标题占用行数\r\n @return 转换后集合\r\n"}, {"name": "importExcelIndustry", "paramTypes": ["java.lang.String", "java.io.InputStream", "int"], "doc": "\n 对excel表单指定表格索引名转换成list（专业专区使用）\r\n\r\n @param sheetName 表格索引名\r\n @param titleNum 标题占用行数\r\n @param is 输入流\r\n @return 转换后集合\r\n"}, {"name": "importExcel", "paramTypes": ["java.lang.String", "java.io.InputStream", "int"], "doc": "\n 对excel表单指定表格索引名转换成list\r\n\r\n @param sheetName 表格索引名\r\n @param titleNum 标题占用行数\r\n @param is 输入流\r\n @return 转换后集合\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param list 导出数据集合\r\n @param sheetName 工作表的名称\r\n @return 结果\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param list 导出数据集合\r\n @param sheetName 工作表的名称\r\n @param title 标题\r\n @return 结果\r\n"}, {"name": "exportExcel", "paramTypes": ["javax.servlet.http.HttpServletResponse", "java.util.List", "java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param response 返回数据\r\n @param list 导出数据集合\r\n @param sheetName 工作表的名称\r\n @return 结果\r\n"}, {"name": "exportExcel", "paramTypes": ["javax.servlet.http.HttpServletResponse", "java.util.List", "java.lang.String", "java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param response 返回数据\r\n @param list 导出数据集合\r\n @param sheetName 工作表的名称\r\n @param title 标题\r\n @return 结果\r\n"}, {"name": "importTemplateExcel", "paramTypes": ["java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param sheetName 工作表的名称\r\n @return 结果\r\n"}, {"name": "importTemplateExcel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param sheetName 工作表的名称\r\n @param title 标题\r\n @return 结果\r\n"}, {"name": "importTemplateExcel", "paramTypes": ["javax.servlet.http.HttpServletResponse", "java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param sheetName 工作表的名称\r\n @return 结果\r\n"}, {"name": "importTemplateExcel", "paramTypes": ["javax.servlet.http.HttpServletResponse", "java.lang.String", "java.lang.String"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @param sheetName 工作表的名称\r\n @param title 标题\r\n @return 结果\r\n"}, {"name": "exportExcel", "paramTypes": ["javax.servlet.http.HttpServletResponse"], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @return 结果\r\n"}, {"name": "exportExcel", "paramTypes": [], "doc": "\n 对list数据源将其里面的数据导入到excel表单\r\n\r\n @return 结果\r\n"}, {"name": "writeSheet", "paramTypes": [], "doc": "\n 创建写入数据到Sheet\r\n"}, {"name": "fillExcelData", "paramTypes": ["int", "org.apache.poi.ss.usermodel.Row"], "doc": "\n 填充excel数据\r\n\r\n @param index 序号\r\n @param row 单元格行\r\n"}, {"name": "createStyles", "paramTypes": ["org.apache.poi.ss.usermodel.Workbook"], "doc": "\n 创建表格样式\r\n\r\n @param wb 工作薄对象\r\n @return 样式列表\r\n"}, {"name": "annotationHeaderStyles", "paramTypes": ["org.apache.poi.ss.usermodel.Workbook", "java.util.Map"], "doc": "\n 根据Excel注解创建表格头样式\r\n\r\n @param wb 工作薄对象\r\n @return 自定义样式列表\r\n"}, {"name": "annotationDataStyles", "paramTypes": ["org.apache.poi.ss.usermodel.Workbook"], "doc": "\n 根据Excel注解创建表格列样式\r\n\r\n @param wb 工作薄对象\r\n @return 自定义样式列表\r\n"}, {"name": "annotationDataStyles", "paramTypes": ["java.util.Map", "java.lang.reflect.Field", "org.dromara.business.annotation.Excel"], "doc": "\n 根据Excel注解创建表格列样式\r\n\r\n @param styles 自定义样式列表\r\n @param field  属性列信息\r\n @param excel  注解信息\r\n"}, {"name": "createHeadCell", "paramTypes": ["org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Row", "int"], "doc": "\n 创建单元格\r\n"}, {"name": "setCellVo", "paramTypes": ["java.lang.Object", "org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Cell"], "doc": "\n 设置单元格信息\r\n\r\n @param value 单元格值\r\n @param attr 注解相关\r\n @param cell 单元格信息\r\n"}, {"name": "getDrawingPatriarch", "paramTypes": ["org.apache.poi.ss.usermodel.Sheet"], "doc": "\n 获取画布\r\n"}, {"name": "getImageType", "paramTypes": ["byte[]"], "doc": "\n 获取图片类型,设置图片插入类型\r\n"}, {"name": "setDataValidation", "paramTypes": ["org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Row", "int"], "doc": "\n 创建表格样式\r\n"}, {"name": "addCell", "paramTypes": ["org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Row", "java.lang.Object", "java.lang.reflect.Field", "int"], "doc": "\n 添加单元格\r\n"}, {"name": "setPromptOrValidation", "paramTypes": ["org.apache.poi.ss.usermodel.Sheet", "java.lang.String[]", "java.lang.String", "int", "int", "int", "int"], "doc": "\n 设置 POI XSSFSheet 单元格提示或选择框\r\n\r\n @param sheet 表单\r\n @param textlist 下拉框显示的内容\r\n @param promptContent 提示内容\r\n @param firstRow 开始行\r\n @param endRow 结束行\r\n @param firstCol 开始列\r\n @param endCol 结束列\r\n"}, {"name": "setXSSFValidationWithHidden", "paramTypes": ["org.apache.poi.ss.usermodel.Sheet", "java.lang.String[]", "java.lang.String", "int", "int", "int", "int"], "doc": "\n 设置某些列的值只能输入预制的数据,显示下拉框（兼容超出一定数量的下拉框）.\r\n\r\n @param sheet 要设置的sheet.\r\n @param textlist 下拉框显示的内容\r\n @param promptContent 提示内容\r\n @param firstRow 开始行\r\n @param endRow 结束行\r\n @param firstCol 开始列\r\n @param endCol 结束列\r\n"}, {"name": "convertByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 解析导出值 0=男,1=女,2=未知\r\n\r\n @param propertyValue 参数值\r\n @param converterExp 翻译注解\r\n @param separator 分隔符\r\n @return 解析后值\r\n"}, {"name": "reverseByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 反向解析值 男=0,女=1,未知=2\r\n\r\n @param propertyValue 参数值\r\n @param converterExp 翻译注解\r\n @param separator 分隔符\r\n @return 解析后值\r\n"}, {"name": "convertDictByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 解析字典值\r\n\r\n @param dictValue 字典值\r\n @param dictType 字典类型\r\n @param separator 分隔符\r\n @return 字典标签\r\n"}, {"name": "reverseDictByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 反向解析值字典值\r\n\r\n @param dictLabel 字典标签\r\n @param dictType 字典类型\r\n @param separator 分隔符\r\n @return 字典值\r\n"}, {"name": "dataFormatHandlerAdapter", "paramTypes": ["java.lang.Object", "org.dromara.business.annotation.Excel", "org.apache.poi.ss.usermodel.Cell"], "doc": "\n 数据处理器\r\n\r\n @param value 数据值\r\n @param excel 数据注解\r\n @return\r\n"}, {"name": "addStatisticsData", "paramTypes": ["java.lang.Integer", "java.lang.String", "org.dromara.business.annotation.Excel"], "doc": "\n 合计统计信息\r\n"}, {"name": "addStatisticsRow", "paramTypes": [], "doc": "\n 创建统计行\r\n"}, {"name": "encodingFilename", "paramTypes": ["java.lang.String"], "doc": "\n 编码文件名\r\n"}, {"name": "getAbsoluteFile", "paramTypes": ["java.lang.String"], "doc": "\n 获取下载路径\r\n\r\n @param filename 文件名称\r\n"}, {"name": "getTargetValue", "paramTypes": ["java.lang.Object", "java.lang.reflect.Field", "org.dromara.business.annotation.Excel"], "doc": "\n 获取bean中的属性值\r\n\r\n @param vo 实体对象\r\n @param field 字段\r\n @param excel 注解\r\n @return 最终的属性值\r\n @throws Exception\r\n"}, {"name": "getValue", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": "\n 以类的属性的get方法方法形式获取值\r\n\r\n @param o\r\n @param name\r\n @return value\r\n @throws Exception\r\n"}, {"name": "createExcelField", "paramTypes": [], "doc": "\n 得到所有定义字段\r\n"}, {"name": "getFields", "paramTypes": [], "doc": "\n 获取字段注解信息\r\n"}, {"name": "getRowHeight", "paramTypes": [], "doc": "\n 根据注解获取最大行高\r\n"}, {"name": "createWorkbook", "paramTypes": [], "doc": "\n 创建一个工作簿\r\n"}, {"name": "createSheet", "paramTypes": ["int", "int"], "doc": "\n 创建工作表\r\n\r\n @param sheetNo sheet数量\r\n @param index 序号\r\n"}, {"name": "getCellValue", "paramTypes": ["org.apache.poi.ss.usermodel.Row", "int"], "doc": "\n 获取单元格值\r\n\r\n @param row 获取的行\r\n @param column 获取单元格列号\r\n @return 单元格值\r\n"}, {"name": "isRowEmpty", "paramTypes": ["org.apache.poi.ss.usermodel.Row"], "doc": "\n 判断是否是空行\r\n\r\n @param row 判断的行\r\n @return\r\n"}, {"name": "getSheetPictures03", "paramTypes": ["org.apache.poi.hssf.usermodel.HSSFSheet", "org.apache.poi.hssf.usermodel.HSSFWorkbook"], "doc": "\n 获取Excel2003图片\r\n\r\n @param sheet 当前sheet对象\r\n @param workbook 工作簿对象\r\n @return Map key:图片单元格索引（1_1）String，value:图片流PictureData\r\n"}, {"name": "getSheetPictures07", "paramTypes": ["org.apache.poi.xssf.usermodel.XSSFSheet", "org.apache.poi.xssf.usermodel.XSSFWorkbook"], "doc": "\n 获取Excel2007图片\r\n\r\n @param sheet 当前sheet对象\r\n @param workbook 工作簿对象\r\n @return Map key:图片单元格索引（1_1）String，value:图片流PictureData\r\n"}, {"name": "parseDateToStr", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 格式化不同类型的日期对象\r\n\r\n @param dateFormat 日期格式\r\n @param val 被格式化的日期对象\r\n @return 格式化后的日期字符\r\n"}, {"name": "isSubList", "paramTypes": [], "doc": "\n 是否有对象的子列表\r\n"}, {"name": "isSubListValue", "paramTypes": ["java.lang.Object"], "doc": "\n 是否有对象的子列表，集合不为空\r\n"}, {"name": "getListCellValue", "paramTypes": ["java.lang.Object"], "doc": "\n 获取集合的值\r\n"}, {"name": "getSubMethod", "paramTypes": ["java.lang.String", "java.lang.Class"], "doc": "\n 获取对象的子列表方法\r\n\r\n @param name 名称\r\n @param pojoClass 类对象\r\n @return 子列表方法\r\n"}], "constructors": []}
{"doc": "\n 留言箱Service业务层处理\r\n \r\n <AUTHOR>\r\n @date 2024-06-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询留言箱\r\n \r\n @param id 留言箱主键\r\n @return 留言箱\r\n"}, {"name": "selectNekndMessageBoxList", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 查询留言箱列表\r\n \r\n @param nekndMessageBox 留言箱\r\n @return 留言箱\r\n"}, {"name": "insertNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 新增留言箱\r\n \r\n @param nekndMessageBox 留言箱\r\n @return 结果\r\n"}, {"name": "updateNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 修改留言箱\r\n \r\n @param nekndMessageBox 留言箱\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageBoxByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除留言箱\r\n \r\n @param ids 需要删除的留言箱主键\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除留言箱信息\r\n \r\n @param id 留言箱主键\r\n @return 结果\r\n"}, {"name": "updateReadStatusByParentId", "paramTypes": ["java.lang.Integer"], "doc": "\n 更新已读状态\r\n \r\n @param parentId 父级ID\r\n @return 结果\r\n"}, {"name": "getCountUnReadCStatus", "paramTypes": ["java.lang.Long", "java.lang.Integer"], "doc": "\n 获取未读消息数量\r\n \r\n @param userId 用户ID\r\n @param topicId 主题ID\r\n @return 未读数量\r\n"}, {"name": "selectAdminMessageBoxList", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 查询管理员留言箱列表\r\n \r\n @param nekndMessageBox 留言箱\r\n @return 留言箱集合\r\n"}, {"name": "deleteNekndMessageBoxByParentId", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据父级ID删除留言箱\r\n \r\n @param id 父级ID\r\n @return 结果\r\n"}, {"name": "updateSenderReadStatusByParentId", "paramTypes": ["java.lang.Integer", "int"], "doc": "\n 更新发送者已读状态\r\n \r\n @param parentId 父级ID\r\n @param senderUserId 发送者用户ID\r\n @return 结果\r\n"}], "constructors": []}
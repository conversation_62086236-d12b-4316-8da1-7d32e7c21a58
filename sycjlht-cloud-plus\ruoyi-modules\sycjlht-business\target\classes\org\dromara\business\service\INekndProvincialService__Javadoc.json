{"doc": "\n 国级Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndProvincialByPid", "paramTypes": ["java.lang.Long"], "doc": "\n 查询省级\r\n\r\n @param pid 省级主键\r\n @return 省级\r\n"}, {"name": "selectNekndProvincialList", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": "\n 查询省级列表\r\n\r\n @param nekndProvincial 省级\r\n @return 省级集合\r\n"}, {"name": "insertNekndProvincial", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": "\n 新增省级\r\n\r\n @param nekndProvincial 省级\r\n @return 结果\r\n"}, {"name": "updateNekndProvincial", "paramTypes": ["org.dromara.business.domain.NekndProvincial"], "doc": "\n 修改省级\r\n\r\n @param nekndProvincial 省级\r\n @return 结果\r\n"}, {"name": "deleteNekndProvincialByPids", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除省级\r\n\r\n @param pids 需要删除的省级主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndProvincialByPid", "paramTypes": ["java.lang.Long"], "doc": "\n 删除省级信息\r\n\r\n @param pid 省级主键\r\n @return 结果\r\n"}], "constructors": []}
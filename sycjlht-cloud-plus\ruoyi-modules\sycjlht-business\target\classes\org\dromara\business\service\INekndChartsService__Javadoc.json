{"doc": "\n 报告管理Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndChartsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询报告管理\r\n\r\n @param id 报告管理主键\r\n @return 报告管理\r\n"}, {"name": "selectNekndChartsList", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": "\n 查询报告管理列表\r\n\r\n @param nekndCharts 报告管理\r\n @return 报告管理集合\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": "\n 新增报告管理\r\n\r\n @param nekndCharts 报告管理\r\n @return 结果\r\n"}, {"name": "updateNekndCharts", "paramTypes": ["org.dromara.business.domain.NekndCharts"], "doc": "\n 修改报告管理\r\n\r\n @param nekndCharts 报告管理\r\n @return 结果\r\n"}, {"name": "deleteNekndChartsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除报告管理\r\n\r\n @param ids 需要删除的报告管理主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndChartsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除报告管理信息\r\n\r\n @param id 报告管理主键\r\n @return 结果\r\n"}], "constructors": []}
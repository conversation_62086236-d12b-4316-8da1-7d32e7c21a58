{"doc": "\n 技术标准立项公告信息Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-12-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTechnicalStandardProjectById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询技术标准立项公告信息\r\n\r\n @param id 技术标准立项公告信息主键\r\n @return 技术标准立项公告信息\r\n"}, {"name": "selectNekndTechnicalStandardProjectList", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": "\n 查询技术标准立项公告信息列表\r\n\r\n @param nekndTechnicalStandardProject 技术标准立项公告信息\r\n @return 技术标准立项公告信息\r\n"}, {"name": "insertNekndTechnicalStandardProject", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": "\n 新增技术标准立项公告信息\r\n\r\n @param nekndTechnicalStandardProject 技术标准立项公告信息\r\n @return 结果\r\n"}, {"name": "updateNekndTechnicalStandardProject", "paramTypes": ["org.dromara.business.domain.NekndTechnicalStandardProject"], "doc": "\n 修改技术标准立项公告信息\r\n\r\n @param nekndTechnicalStandardProject 技术标准立项公告信息\r\n @return 结果\r\n"}, {"name": "deleteNekndTechnicalStandardProjectByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除技术标准立项公告信息\r\n\r\n @param ids 需要删除的技术标准立项公告信息主键\r\n @return 结果\r\n"}, {"name": "deleteNekndTechnicalStandardProjectById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除技术标准立项公告信息信息\r\n\r\n @param id 技术标准立项公告信息主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "\n 师资库Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-12-07\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTeacherPoolById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询师资库\r\n\r\n @param id 师资库主键\r\n @return 师资库\r\n"}, {"name": "selectNekndTeacherPoolList", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": "\n 查询师资库列表\r\n\r\n @param nekndTeacherPool 师资库\r\n @return 师资库\r\n"}, {"name": "insertNekndTeacherPool", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": "\n 新增师资库\r\n\r\n @param nekndTeacherPool 师资库\r\n @return 结果\r\n"}, {"name": "updateNekndTeacherPool", "paramTypes": ["org.dromara.business.domain.NekndTeacherPool"], "doc": "\n 修改师资库\r\n\r\n @param nekndTeacherPool 师资库\r\n @return 结果\r\n"}, {"name": "deleteNekndTeacherPoolByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除师资库\r\n\r\n @param ids 需要删除的师资库主键\r\n @return 结果\r\n"}, {"name": "deleteNekndTeacherPoolById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除师资库信息\r\n\r\n @param id 师资库主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "\n 招聘岗位Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询招聘岗位\r\n\r\n @param id 招聘岗位主键\r\n @return 招聘岗位\r\n"}, {"name": "selectNekndEmployList", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 查询招聘岗位列表\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 招聘岗位\r\n"}, {"name": "selectNekndEmployListReview", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 审核查询招聘岗位列表\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 招聘岗位\r\n"}, {"name": "insertNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 新增招聘岗位\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 结果\r\n"}, {"name": "updateNekndEmploy", "paramTypes": ["org.dromara.business.domain.NekndEmploy"], "doc": "\n 修改招聘岗位\r\n\r\n @param nekndEmploy 招聘岗位\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除招聘岗位\r\n\r\n @param ids 需要删除的招聘岗位主键\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除招聘岗位信息\r\n\r\n @param id 招聘岗位主键\r\n @return 结果\r\n"}, {"name": "getAIJobContent", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n AI智能填充岗位描述或要求\r\n\r\n @param jobName 岗位名称\r\n @param type 填充类型：1-职位描述，2-任职要求\r\n @return 生成的内容\r\n"}, {"name": "getEmployMatch", "paramTypes": ["java.lang.Integer", "java.lang.Long"], "doc": "\n 分析岗位与人才匹配度\r\n\r\n @param employId 岗位ID\r\n @param userId 用户ID\r\n @return 匹配结果\r\n"}], "constructors": []}
{"doc": "\n 培训(证书/活动/项目)Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询培训(证书/活动/项目)\r\n\r\n @param id 培训(证书/活动/项目)主键\r\n @return 培训(证书/活动/项目)\r\n"}, {"name": "selectNekndTrainList", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": "\n 查询培训(证书/活动/项目)列表\r\n\r\n @param nekndTrain 培训(证书/活动/项目)\r\n @return 培训(证书/活动/项目)\r\n"}, {"name": "insertNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": "\n 新增培训(证书/活动/项目)\r\n\r\n @param nekndTrain 培训(证书/活动/项目)\r\n @return 结果\r\n"}, {"name": "updateNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": "\n 修改培训(证书/活动/项目)\r\n\r\n @param nekndTrain 培训(证书/活动/项目)\r\n @return 结果\r\n"}, {"name": "deleteNekndTrainByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除培训(证书/活动/项目)\r\n\r\n @param ids 需要删除的培训(证书/活动/项目)主键\r\n @return 结果\r\n"}, {"name": "deleteNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除培训(证书/活动/项目)信息\r\n\r\n @param id 培训(证书/活动/项目)主键\r\n @return 结果\r\n"}, {"name": "getCertificateList", "paramTypes": [], "doc": "\n 获取证书列表\r\n @return\r\n"}, {"name": "getCertificateById", "paramTypes": ["java.lang.Integer"], "doc": "\n 获取证书详情\r\n"}], "constructors": []}
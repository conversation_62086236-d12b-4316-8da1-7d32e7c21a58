<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndCommunityMapper">
    
    <resultMap type="NekndCommunity" id="NekndCommunityResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="url"    column="url"    />
        <result property="school"    column="school"    />
        <result property="type"    column="type"    />
    </resultMap>

    <sql id="selectNekndCommunityVo">
        select id, name, url, school, type from neknd_community
    </sql>

    <select id="selectNekndCommunityList" parameterType="NekndCommunity" resultMap="NekndCommunityResult">
        <include refid="selectNekndCommunityVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name = #{name}</if>
            <if test="school != null "> and school = #{school}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
        </where>
    </select>
    
    <select id="selectNekndCommunityById" parameterType="Integer" resultMap="NekndCommunityResult">
        <include refid="selectNekndCommunityVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndCommunity" parameterType="NekndCommunity" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_community
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="url != null">url,</if>
            <if test="school != null">school,</if>
            <if test="type != null">type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="url != null">#{url},</if>
            <if test="school != null">#{school},</if>
            <if test="type != null">#{type},</if>
         </trim>
    </insert>

    <update id="updateNekndCommunity" parameterType="NekndCommunity">
        update neknd_community
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="url != null">url = #{url},</if>
            <if test="school != null">school = #{school},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndCommunityById" parameterType="Integer">
        delete from neknd_community where id = #{id}
    </delete>

    <delete id="deleteNekndCommunityByIds" parameterType="String">
        delete from neknd_community where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
{"doc": "\n 资源共建（校企合作）Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-11-06\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndResourcesConservationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询资源共建（校企合作）\r\n\r\n @param id 资源共建（校企合作）主键\r\n @return 资源共建（校企合作）\r\n"}, {"name": "selectNekndResourcesConservationList", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 查询资源共建（校企合作）列表\r\n\r\n @param nekndResourcesConservation 资源共建（校企合作）\r\n @return 资源共建（校企合作）集合\r\n"}, {"name": "selectNekndResourcesConservationListCompany", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 查询资源共建（校企合作）列表\r\n\r\n\r\n @return 资源共建（校企合作）集合\r\n"}, {"name": "selectNekndResourcesConservationListGovernment", "paramTypes": [], "doc": "\n 查询资源共建（校企合作）列表\r\n\r\n\r\n @return 资源共建（校企合作）集合\r\n"}, {"name": "insertNekndResourcesConservation", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 新增资源共建（校企合作）\r\n\r\n @param nekndResourcesConservation 资源共建（校企合作）\r\n @return 结果\r\n"}, {"name": "updateNekndResourcesConservation", "paramTypes": ["org.dromara.business.domain.NekndResourcesConservation"], "doc": "\n 修改资源共建（校企合作）\r\n\r\n @param nekndResourcesConservation 资源共建（校企合作）\r\n @return 结果\r\n"}, {"name": "deleteNekndResourcesConservationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除资源共建（校企合作）\r\n\r\n @param id 资源共建（校企合作）主键\r\n @return 结果\r\n"}, {"name": "deleteNekndResourcesConservationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除资源共建（校企合作）\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
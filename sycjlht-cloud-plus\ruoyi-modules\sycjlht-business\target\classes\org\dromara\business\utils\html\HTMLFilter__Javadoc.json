{"doc": "\n HTML过滤器，用于去除XSS漏洞隐患。\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "REGEX_FLAGS_SI", "doc": "\n regex flag union representing /si modifiers in php\r\n"}, {"name": "vAllowed", "doc": "\n set of allowed html elements, along with allowed attributes for each element\r\n"}, {"name": "vTagCounts", "doc": "\n counts of open tags for each (allowable) html element\r\n"}, {"name": "vSelfClosingTags", "doc": "\n html elements which must always be self-closing (e.g. \"<img />\")\r\n"}, {"name": "vNeedClosingTags", "doc": "\n html elements which must always have separate opening and closing tags (e.g. \"<b></b>\")\r\n"}, {"name": "vDisallowed", "doc": "\n set of disallowed html elements\r\n"}, {"name": "vProtocolAtts", "doc": "\n attributes which should be checked for valid protocols\r\n"}, {"name": "vAllowedProtocols", "doc": "\n allowed protocols\r\n"}, {"name": "vRemoveBlanks", "doc": "\n tags which should be removed if they contain no content (e.g. \"<b></b>\" or \"<b />\")\r\n"}, {"name": "vAllowedEntities", "doc": "\n entities allowed within html markup\r\n"}, {"name": "stripComment", "doc": "\n flag determining whether comments are allowed in input String.\r\n"}, {"name": "alwaysMakeTags", "doc": "\n flag determining whether to try to make tags when presented with \"unbalanced\" angle brackets (e.g. \"<b text </b>\"\r\n becomes \"<b> text </b>\"). If set to false, unbalanced angle brackets will be html escaped.\r\n"}], "enumConstants": [], "methods": [{"name": "filter", "paramTypes": ["java.lang.String"], "doc": "\n given a user submitted input String, filter out any invalid or restricted html.\r\n\r\n @param input text (i.e. submitted by a user) than may contain html\r\n @return \"clean\" version of input, with only valid, whitelisted html elements allowed\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Default constructor.\r\n"}, {"name": "<init>", "paramTypes": ["java.util.Map"], "doc": "\n Map-parameter configurable constructor.\r\n\r\n @param conf map containing configuration. keys match field names.\r\n"}]}
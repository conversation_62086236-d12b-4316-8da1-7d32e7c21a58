{"doc": "\n 平台迁移Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2025-02-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPlatformConfigById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询平台迁移\r\n\r\n @param id 平台迁移主键\r\n @return 平台迁移\r\n"}, {"name": "selectNekndPlatformConfigList", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": "\n 查询平台迁移列表\r\n\r\n @param nekndPlatformConfig 平台迁移\r\n @return 平台迁移\r\n"}, {"name": "insertNekndPlatformConfig", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": "\n 新增平台迁移\r\n\r\n @param nekndPlatformConfig 平台迁移\r\n @return 结果\r\n"}, {"name": "updateNekndPlatformConfig", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfig"], "doc": "\n 修改平台迁移\r\n\r\n @param nekndPlatformConfig 平台迁移\r\n @return 结果\r\n"}, {"name": "deleteNekndPlatformConfigByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除平台迁移\r\n\r\n @param ids 需要删除的平台迁移主键\r\n @return 结果\r\n"}, {"name": "deleteNekndPlatformConfigById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除平台迁移信息\r\n\r\n @param id 平台迁移主键\r\n @return 结果\r\n"}], "constructors": []}
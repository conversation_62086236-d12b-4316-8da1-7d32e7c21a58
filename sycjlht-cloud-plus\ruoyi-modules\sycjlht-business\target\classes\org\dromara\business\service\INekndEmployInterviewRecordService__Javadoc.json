{"doc": "\n AI面试记录Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-29\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndEmployInterviewRecordById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询AI面试记录\r\n\r\n @param id AI面试记录主键\r\n @return AI面试记录\r\n"}, {"name": "selectNekndEmployInterviewRecordList", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": "\n 查询AI面试记录列表\r\n\r\n @param nekndEmployInterviewRecord AI面试记录\r\n @return AI面试记录集合\r\n"}, {"name": "insertNekndEmployInterviewRecord", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": "\n 新增AI面试记录\r\n\r\n @param nekndEmployInterviewRecord AI面试记录\r\n @return 结果\r\n"}, {"name": "updateNekndEmployInterviewRecord", "paramTypes": ["org.dromara.business.domain.NekndEmployInterviewRecord"], "doc": "\n 修改AI面试记录\r\n\r\n @param nekndEmployInterviewRecord AI面试记录\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployInterviewRecordByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除AI面试记录\r\n\r\n @param ids 需要删除的AI面试记录主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployInterviewRecordById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除AI面试记录信息\r\n\r\n @param id AI面试记录主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "\n 学生调查Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-21\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPersonInformationSurveyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询学生调查\r\n\r\n @param id 学生调查主键\r\n @return 学生调查\r\n"}, {"name": "selectNekndPersonInformationSurveyList", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 查询学生调查列表\r\n\r\n @param nekndPersonInformationSurvey 学生调查\r\n @return 学生调查集合\r\n"}, {"name": "insertNekndPersonInformationSurvey", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 新增学生调查\r\n\r\n @param nekndPersonInformationSurvey 学生调查\r\n @return 结果\r\n"}, {"name": "updateNekndPersonInformationSurvey", "paramTypes": ["org.dromara.business.domain.NekndPersonInformationSurvey"], "doc": "\n 修改学生调查\r\n\r\n @param nekndPersonInformationSurvey 学生调查\r\n @return 结果\r\n"}, {"name": "deleteNekndPersonInformationSurveyByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除学生调查\r\n\r\n @param ids 需要删除的学生调查主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndPersonInformationSurveyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除学生调查信息\r\n\r\n @param id 学生调查主键\r\n @return 结果\r\n"}, {"name": "selectFormY", "paramTypes": ["java.lang.String"], "doc": "\n 查询填写和未填写问卷调查的人员\r\n @return\r\n"}, {"name": "getItem1Counts", "paramTypes": [], "doc": "\n 就业信息统计\r\n\r\n @return 个人信息集合\r\n"}], "constructors": []}
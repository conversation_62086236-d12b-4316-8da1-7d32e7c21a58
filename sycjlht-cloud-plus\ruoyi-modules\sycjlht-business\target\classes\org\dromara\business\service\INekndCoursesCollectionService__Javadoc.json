{"doc": "\n 课程收藏记录Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesCollectionById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询课程收藏记录\r\n\r\n @param id 课程收藏记录主键\r\n @return 课程收藏记录\r\n"}, {"name": "selectNekndCoursesCollectionList", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 查询课程收藏记录列表\r\n\r\n @param nekndCoursesCollection 课程收藏记录\r\n @return 课程收藏记录集合\r\n"}, {"name": "insertNekndCoursesCollection", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 新增课程收藏记录\r\n\r\n @param nekndCoursesCollection 课程收藏记录\r\n @return 结果\r\n"}, {"name": "updateNekndCoursesCollection", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 修改课程收藏记录\r\n\r\n @param nekndCoursesCollection 课程收藏记录\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesCollectionByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除课程收藏记录\r\n\r\n @param ids 需要删除的课程收藏记录主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesCollectionById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除课程收藏记录信息\r\n\r\n @param id 课程收藏记录主键\r\n @return 结果\r\n"}, {"name": "selectFavoriteCountByCourseId", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据课程id查询收藏数量\r\n @param courseId\r\n @return 收藏数量\r\n"}], "constructors": []}
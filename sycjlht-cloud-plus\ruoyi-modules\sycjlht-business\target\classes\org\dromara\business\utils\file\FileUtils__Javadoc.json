{"doc": "\n 文件处理工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "writeBytes", "paramTypes": ["java.lang.String", "java.io.OutputStream"], "doc": "\n 输出指定文件的byte数组\r\n\r\n @param filePath 文件路径\r\n @param os 输出流\r\n @return\r\n"}, {"name": "writeImportBytes", "paramTypes": ["byte[]"], "doc": "\n 写数据到文件中\r\n\r\n @param data 数据\r\n @return 目标文件\r\n @throws IOException IO异常\r\n"}, {"name": "writeBytes", "paramTypes": ["byte[]", "java.lang.String"], "doc": "\n 写数据到文件中\r\n\r\n @param data 数据\r\n @param uploadDir 目标文件\r\n @return 目标文件\r\n @throws IOException IO异常\r\n"}, {"name": "deleteFile", "paramTypes": ["java.lang.String"], "doc": "\n 删除文件\r\n\r\n @param filePath 文件\r\n @return\r\n"}, {"name": "isValidFilename", "paramTypes": ["java.lang.String"], "doc": "\n 文件名称验证\r\n\r\n @param filename 文件名称\r\n @return true 正常 false 非法\r\n"}, {"name": "checkAllowDownload", "paramTypes": ["java.lang.String"], "doc": "\n 检查文件是否可下载\r\n\r\n @param resource 需要下载的文件\r\n @return true 正常 false 非法\r\n"}, {"name": "setFileDownloadHeader", "paramTypes": ["javax.servlet.http.HttpServletRequest", "java.lang.String"], "doc": "\n 下载文件名重新编码\r\n\r\n @param request 请求对象\r\n @param fileName 文件名\r\n @return 编码后的文件名\r\n"}, {"name": "setAttachmentResponseHeader", "paramTypes": ["javax.servlet.http.HttpServletResponse", "java.lang.String"], "doc": "\n 下载文件名重新编码\r\n\r\n @param response 响应对象\r\n @param realFileName 真实文件名\r\n"}, {"name": "percentEncode", "paramTypes": ["java.lang.String"], "doc": "\n 百分号编码工具方法\r\n\r\n @param s 需要百分号编码的字符串\r\n @return 百分号编码后的字符串\r\n"}, {"name": "getFileExtendName", "paramTypes": ["byte[]"], "doc": "\n 获取图像后缀\r\n\r\n @param photoByte 图像数据\r\n @return 后缀名\r\n"}, {"name": "getName", "paramTypes": ["java.lang.String"], "doc": "\n 获取文件名称 /profile/upload/2022/04/16/ruoyi.png -- ruoyi.png\r\n\r\n @param fileName 路径名称\r\n @return 没有文件路径的名称\r\n"}, {"name": "getNameNotSuffix", "paramTypes": ["java.lang.String"], "doc": "\n 获取不带后缀文件名称 /profile/upload/2022/04/16/ruoyi.png -- ruoyi\r\n\r\n @param fileName 路径名称\r\n @return 没有文件路径和后缀的名称\r\n"}], "constructors": []}
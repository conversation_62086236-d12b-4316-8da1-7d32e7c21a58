<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndCoursesMapper">
    
    <resultMap type="NekndCourses" id="NekndCoursesResult">
        <result property="courseId"    column="course_id"    />
        <result property="title"    column="title"    />
        <result property="description"    column="description"    />
        <result property="coverImageUrl"    column="cover_image_url"    />
        <result property="viewCount"    column="view_count"    />
        <result property="favoriteCount"    column="favorite_count"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createId"    column="create_id"    />
        <result property="updateId"    column="update_id"    />
        <result property="category"    column="category"    />
        <result property="likeCount"    column="like_count"    />
    </resultMap>

    <sql id="selectNekndCoursesVo">
        select course_id, title, description, cover_image_url, view_count, favorite_count, del_flag, create_by, create_time, update_by, update_time, create_id, update_id,category,like_count from neknd_courses
    </sql>

    <select id="selectNekndCoursesList" parameterType="NekndCourses" resultMap="NekndCoursesResult">
        <include refid="selectNekndCoursesVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="category != null  and category != ''"> and category =#{category}</if>
        </where>
    </select>

    <select id="selectNekndCoursesListWithoutCourseId" parameterType="Integer" resultMap="NekndCoursesResult">
        <include refid="selectNekndCoursesVo"/>
        where course_id != #{courseId}
    </select>
    
    <select id="selectNekndCoursesByCourseId" parameterType="Integer" resultMap="NekndCoursesResult">
        <include refid="selectNekndCoursesVo"/>
        where course_id = #{courseId}
    </select>
        
    <insert id="insertNekndCourses" parameterType="NekndCourses" useGeneratedKeys="true" keyProperty="courseId">
        insert into neknd_courses
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="description != null">description,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="favoriteCount != null">favorite_count,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="category != null">category,</if>
            <if test="likeCount != null">like_count,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="favoriteCount != null">#{favoriteCount},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createId != null">#{createId},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="category != null">#{category},</if>
            <if test="likeCount != null">#{likeCount},</if>
         </trim>
    </insert>

    <update id="updateNekndCourses" parameterType="NekndCourses">
        update neknd_courses
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="favoriteCount != null">favorite_count = #{favoriteCount},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="category != null">category = #{category},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
        </trim>
        where course_id = #{courseId}
    </update>

    <delete id="deleteNekndCoursesByCourseId" parameterType="Integer">
        delete from neknd_courses where course_id = #{courseId}
    </delete>

    <delete id="deleteNekndCoursesByCourseIds" parameterType="String">
        delete from neknd_courses where course_id in 
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>
</mapper>
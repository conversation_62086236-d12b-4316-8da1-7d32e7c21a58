{"doc": "\n 市域产教融合项目库Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-02-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询市域产教融合项目库\r\n\r\n @param id 市域产教融合项目库主键\r\n @return 市域产教融合项目库\r\n"}, {"name": "selectNekndIndustryEducationProjectsList", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": "\n 查询市域产教融合项目库列表\r\n\r\n @param nekndIndustryEducationProjects 市域产教融合项目库\r\n @return 市域产教融合项目库集合\r\n"}, {"name": "insertNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": "\n 新增市域产教融合项目库\r\n\r\n @param nekndIndustryEducationProjects 市域产教融合项目库\r\n @return 结果\r\n"}, {"name": "updateNekndIndustryEducationProjects", "paramTypes": ["org.dromara.business.domain.NekndIndustryEducationProjects"], "doc": "\n 修改市域产教融合项目库\r\n\r\n @param nekndIndustryEducationProjects 市域产教融合项目库\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryEducationProjectsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除市域产教融合项目库\r\n\r\n @param id 市域产教融合项目库主键\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryEducationProjectsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除市域产教融合项目库\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
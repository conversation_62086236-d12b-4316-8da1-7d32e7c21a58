package org.dromara.business.domain.vo;

import io.github.linpeilie.AutoMapperConfig__489;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.PolicyNews;
import org.dromara.business.domain.PolicyNewsToPolicyNewsVoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__489.class,
    uses = {PolicyNewsToPolicyNewsVoMapper__11.class},
    imports = {}
)
public interface PolicyNewsVoToPolicyNewsMapper__11 extends BaseMapper<PolicyNewsVo, PolicyNews> {
}

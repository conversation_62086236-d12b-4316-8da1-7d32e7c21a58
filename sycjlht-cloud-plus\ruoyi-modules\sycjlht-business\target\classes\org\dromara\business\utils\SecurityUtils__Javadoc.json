{"doc": "\n 安全服务工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserId", "paramTypes": [], "doc": "\n 用户ID\r\n"}, {"name": "getDeptId", "paramTypes": [], "doc": "\n 获取部门ID\r\n"}, {"name": "getUsername", "paramTypes": [], "doc": "\n 获取用户账户\r\n"}, {"name": "getLoginUser", "paramTypes": [], "doc": "\n 获取用户\r\n"}, {"name": "getAuthentication", "paramTypes": [], "doc": "\n 获取Authentication\r\n"}, {"name": "encryptPassword", "paramTypes": ["java.lang.String"], "doc": "\n 生成BCryptPasswordEncoder密码\r\n\r\n @param password 密码\r\n @return 加密字符串\r\n"}, {"name": "matchesPassword", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 判断密码是否相同\r\n\r\n @param rawPassword 真实密码\r\n @param encodedPassword 加密后字符\r\n @return 结果\r\n"}, {"name": "isAdmin", "paramTypes": ["java.lang.Long"], "doc": "\n 是否为管理员\r\n\r\n @param userId 用户ID\r\n @return 结果\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 验证用户是否具备某权限\r\n\r\n @param permission 权限字符串\r\n @return 用户是否具备某权限\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.util.Collection", "java.lang.String"], "doc": "\n 判断是否包含权限\r\n\r\n @param authorities 权限列表\r\n @param permission 权限字符串\r\n @return 用户是否具备某权限\r\n"}, {"name": "hasRole", "paramTypes": ["java.lang.String"], "doc": "\n 验证用户是否拥有某个角色\r\n\r\n @param role 角色标识\r\n @return 用户是否具备某角色\r\n"}, {"name": "hasRole", "paramTypes": ["java.util.Collection", "java.lang.String"], "doc": "\n 判断是否包含角色\r\n\r\n @param roles 角色列表\r\n @param role 角色\r\n @return 用户是否具备某角色权限\r\n"}], "constructors": []}
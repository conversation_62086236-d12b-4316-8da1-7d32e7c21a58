{"doc": "\n 培训报名Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-06-19\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询培训报名\r\n\r\n @param id 培训报名主键\r\n @return 培训报名\r\n"}, {"name": "selectNekndRegistrationStatusList", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 查询培训报名列表\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 培训报名集合\r\n"}, {"name": "insertNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 新增培训报名\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 结果\r\n"}, {"name": "updateNekndRegistrationStatus", "paramTypes": ["org.dromara.business.domain.NekndRegistrationStatus"], "doc": "\n 修改培训报名\r\n\r\n @param nekndRegistrationStatus 培训报名\r\n @return 结果\r\n"}, {"name": "deleteNekndRegistrationStatusByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除培训报名\r\n\r\n @param ids 需要删除的培训报名主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndRegistrationStatusById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除培训报名信息\r\n\r\n @param id 培训报名主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "\n 科普研学报名记录Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-08-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询科普研学报名记录\r\n\r\n @param id 科普研学报名记录主键\r\n @return 科普研学报名记录\r\n"}, {"name": "selectNekndScientificResearchRegisterRecordsList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": "\n 查询科普研学报名记录列表\r\n\r\n @param nekndScientificResearchRegisterRecords 科普研学报名记录\r\n @return 科普研学报名记录\r\n"}, {"name": "insertNekndScientificResearchRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": "\n 新增科普研学报名记录\r\n\r\n @param nekndScientificResearchRegisterRecords 科普研学报名记录\r\n @return 结果\r\n"}, {"name": "updateNekndScientificResearchRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchRegisterRecords"], "doc": "\n 修改科普研学报名记录\r\n\r\n @param nekndScientificResearchRegisterRecords 科普研学报名记录\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchRegisterRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除科普研学报名记录\r\n\r\n @param ids 需要删除的科普研学报名记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除科普研学报名记录信息\r\n\r\n @param id 科普研学报名记录主键\r\n @return 结果\r\n"}], "constructors": []}
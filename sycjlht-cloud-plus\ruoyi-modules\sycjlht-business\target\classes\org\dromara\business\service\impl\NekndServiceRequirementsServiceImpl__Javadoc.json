{"doc": "\n 服务需求Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-30\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndServiceRequirementsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询服务需求\r\n\r\n @param id 服务需求主键\r\n @return 服务需求\r\n"}, {"name": "selectNekndServiceRequirementsList", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": "\n 查询服务需求列表\r\n\r\n @param nekndServiceRequirements 服务需求\r\n @return 服务需求\r\n"}, {"name": "insertNekndServiceRequirements", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": "\n 新增服务需求\r\n\r\n @param nekndServiceRequirements 服务需求\r\n @return 结果\r\n"}, {"name": "updateNekndServiceRequirements", "paramTypes": ["org.dromara.business.domain.NekndServiceRequirements"], "doc": "\n 修改服务需求\r\n\r\n @param nekndServiceRequirements 服务需求\r\n @return 结果\r\n"}, {"name": "deleteNekndServiceRequirementsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除服务需求\r\n\r\n @param ids 需要删除的服务需求主键\r\n @return 结果\r\n"}, {"name": "deleteNekndServiceRequirementsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除服务需求信息\r\n\r\n @param id 服务需求主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getParticipateData", "paramTypes": [], "doc": "\n 参与园区、企业、高校数\r\n"}, {"name": "getProvincialInvestmentRanking", "paramTypes": [], "doc": "\n 各省投入资金排名\r\n"}, {"name": "getSpecialtyCoverageMatchRate", "paramTypes": [], "doc": "\n 专业覆盖匹配度\r\n"}, {"name": "getProvincialLocalEmploymentRate", "paramTypes": [], "doc": "\n 各省园区本地就业率\r\n"}, {"name": "getProvincialCountryLevelPark", "paramTypes": [], "doc": "\n 各省国家级数量分布\r\n"}, {"name": "getProvincialEnterpriseType", "paramTypes": [], "doc": "\n 各省企业类型数量分布\r\n"}, {"name": "getProvincialComprehensiveRanking", "paramTypes": [], "doc": "\n 各省综合排名\r\n"}, {"name": "getIndustryEducationBenchmark", "paramTypes": [], "doc": "\n 专项冠军\r\n"}, {"name": "getWarningPanel", "paramTypes": [], "doc": "\n 预警面板\r\n"}], "constructors": []}
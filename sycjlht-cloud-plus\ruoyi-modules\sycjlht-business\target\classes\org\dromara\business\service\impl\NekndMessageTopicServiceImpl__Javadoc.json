{"doc": "\n 留言主题Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-08-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询留言主题\r\n\r\n @param id 留言主题主键\r\n @return 留言主题\r\n"}, {"name": "selectNekndMessageTopicList", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 查询留言主题列表\r\n\r\n @param nekndMessageTopic 留言主题\r\n @return 留言主题\r\n"}, {"name": "insertNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 新增留言主题\r\n\r\n @param nekndMessageTopic 留言主题\r\n @return 结果\r\n"}, {"name": "updateNekndMessageTopic", "paramTypes": ["org.dromara.business.domain.NekndMessageTopic"], "doc": "\n 修改留言主题\r\n\r\n @param nekndMessageTopic 留言主题\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageTopicByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除留言主题\r\n\r\n @param ids 需要删除的留言主题主键\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageTopicById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除留言主题信息\r\n\r\n @param id 留言主题主键\r\n @return 结果\r\n"}], "constructors": []}
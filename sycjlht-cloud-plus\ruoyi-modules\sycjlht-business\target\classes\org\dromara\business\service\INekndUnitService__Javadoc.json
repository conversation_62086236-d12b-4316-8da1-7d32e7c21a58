{"doc": "\n 成员单位信息Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-10-22\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询成员单位信息\r\n\r\n @param id 成员单位信息主键\r\n @return 成员单位信息\r\n"}, {"name": "selectNekndUnitList", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": "\n 查询成员单位信息列表\r\n\r\n @param nekndUnit 成员单位信息\r\n @return 成员单位信息集合\r\n"}, {"name": "insertNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": "\n 新增成员单位信息\r\n\r\n @param nekndUnit 成员单位信息\r\n @return 结果\r\n"}, {"name": "updateNekndUnit", "paramTypes": ["org.dromara.business.domain.NekndUnit"], "doc": "\n 修改成员单位信息\r\n\r\n @param nekndUnit 成员单位信息\r\n @return 结果\r\n"}, {"name": "deleteNekndUnitByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除成员单位信息\r\n\r\n @param ids 需要删除的成员单位信息主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndUnitById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除成员单位信息信息\r\n\r\n @param id 成员单位信息主键\r\n @return 结果\r\n"}, {"name": "getIndustrialParkData", "paramTypes": ["java.lang.String"], "doc": "\n 获取产业园区能级-市下数据\r\n"}, {"name": "getIndustrialCityData", "paramTypes": ["java.lang.String"], "doc": "\n 获取产业园区能级-省下数据\r\n"}, {"name": "getIndustrialProvincialData", "paramTypes": ["java.lang.String"], "doc": "\n  获取产业园区能级-国家级下数据\r\n @param name\r\n @return\r\n"}, {"name": "getVisualizeCityData", "paramTypes": ["java.lang.String"], "doc": "\n 获取检测大屏-市数据\r\n @param cityId\r\n"}], "constructors": []}
{"doc": "\n 关于平台信息编辑Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2025-02-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPlatformConfigAboutById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询关于平台信息编辑\r\n\r\n @param id 关于平台信息编辑主键\r\n @return 关于平台信息编辑\r\n"}, {"name": "selectNekndPlatformConfigAboutList", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": "\n 查询关于平台信息编辑列表\r\n\r\n @param nekndPlatformConfigAbout 关于平台信息编辑\r\n @return 关于平台信息编辑\r\n"}, {"name": "insertNekndPlatformConfigAbout", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": "\n 新增关于平台信息编辑\r\n\r\n @param nekndPlatformConfigAbout 关于平台信息编辑\r\n @return 结果\r\n"}, {"name": "updateNekndPlatformConfigAbout", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": "\n 修改关于平台信息编辑\r\n\r\n @param nekndPlatformConfigAbout 关于平台信息编辑\r\n @return 结果\r\n"}, {"name": "deleteNekndPlatformConfigAboutByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除关于平台信息编辑\r\n\r\n @param ids 需要删除的关于平台信息编辑主键\r\n @return 结果\r\n"}, {"name": "deleteNekndPlatformConfigAboutById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除关于平台信息编辑信息\r\n\r\n @param id 关于平台信息编辑主键\r\n @return 结果\r\n"}, {"name": "insertNekndAboutClassify", "paramTypes": ["org.dromara.business.domain.NekndPlatformConfigAbout"], "doc": "\n 新增关于(头部简介)信息信息\r\n\r\n @param nekndPlatformConfigAbout 关于平台信息编辑对象\r\n"}], "constructors": []}
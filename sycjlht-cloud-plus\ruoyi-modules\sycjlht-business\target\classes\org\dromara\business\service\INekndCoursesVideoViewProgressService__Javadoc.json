{"doc": "\n 视频观看进度记录Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesVideoViewProgressById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询视频观看进度记录\r\n\r\n @param id 视频观看进度记录主键\r\n @return 视频观看进度记录\r\n"}, {"name": "selectNekndCoursesVideoViewProgressList", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": "\n 查询视频观看进度记录列表\r\n\r\n @param nekndCoursesVideoViewProgress 视频观看进度记录\r\n @return 视频观看进度记录集合\r\n"}, {"name": "insertNekndCoursesVideoViewProgress", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": "\n 新增视频观看进度记录\r\n\r\n @param nekndCoursesVideoViewProgress 视频观看进度记录\r\n @return 结果\r\n"}, {"name": "updateNekndCoursesVideoViewProgress", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideoViewProgress"], "doc": "\n 修改视频观看进度记录\r\n\r\n @param nekndCoursesVideoViewProgress 视频观看进度记录\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesVideoViewProgressByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除视频观看进度记录\r\n\r\n @param ids 需要删除的视频观看进度记录主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesVideoViewProgressById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除视频观看进度记录信息\r\n\r\n @param id 视频观看进度记录主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "\n 【请填写功能名称】Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-25\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryCooperationStrengthByIndustryId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询【请填写功能名称】\r\n\r\n @param industryId 【请填写功能名称】主键\r\n @return 【请填写功能名称】\r\n"}, {"name": "selectNekndIndustryCooperationStrengthByIndustryIdCount", "paramTypes": ["java.lang.Long"], "doc": "\n 查询对应id记录数\r\n\r\n @param industryId 【请填写功能名称】主键\r\n @return 【记录数】\r\n"}, {"name": "selectNekndIndustryCooperationStrengthList", "paramTypes": ["org.dromara.business.domain.NekndIndustryCooperationStrength"], "doc": "\n 查询【请填写功能名称】列表\r\n\r\n @param nekndIndustryCooperationStrength 【请填写功能名称】\r\n @return 【请填写功能名称】集合\r\n"}, {"name": "insertNekndIndustryCooperationStrength", "paramTypes": ["org.dromara.business.domain.NekndIndustryCooperationStrength"], "doc": "\n 新增【请填写功能名称】\r\n\r\n @param nekndIndustryCooperationStrength 【请填写功能名称】\r\n @return 结果\r\n"}, {"name": "updateNekndIndustryCooperationStrength", "paramTypes": ["org.dromara.business.domain.NekndIndustryCooperationStrength"], "doc": "\n 修改【请填写功能名称】\r\n\r\n @param nekndIndustryCooperationStrength 【请填写功能名称】\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryCooperationStrengthByIndustryId", "paramTypes": ["java.lang.Long"], "doc": "\n 删除【请填写功能名称】\r\n\r\n @param industryId 【请填写功能名称】主键\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryCooperationStrengthByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除【请填写功能名称】\r\n\r\n @param industryIds 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
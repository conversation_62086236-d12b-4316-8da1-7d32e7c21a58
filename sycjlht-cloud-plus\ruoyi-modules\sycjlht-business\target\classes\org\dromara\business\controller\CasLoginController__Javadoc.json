{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "casLogin", "paramTypes": ["javax.servlet.http.HttpServletRequest"], "doc": "\n cas登录 ： 门户登录\r\n @param request\r\n @return\r\n"}, {"name": "logout", "paramTypes": ["javax.servlet.http.HttpServletRequest", "javax.servlet.http.HttpServletResponse"], "doc": "\n cas登录 ： 门户退出\r\n @param request\r\n @return\r\n"}, {"name": "casH5Login", "paramTypes": ["javax.servlet.http.HttpServletRequest"], "doc": "\n cas登录 ： h5登录\r\n @param request\r\n @return\r\n"}, {"name": "logoutH5", "paramTypes": ["javax.servlet.http.HttpServletRequest", "javax.servlet.http.HttpServletResponse"], "doc": "\n cas登录 ： 门户退出\r\n @param request\r\n @return\r\n"}], "constructors": []}
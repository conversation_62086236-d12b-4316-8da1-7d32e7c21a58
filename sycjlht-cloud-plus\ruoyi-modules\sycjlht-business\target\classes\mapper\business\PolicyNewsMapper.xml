<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.PolicyNewsMapper">

    <resultMap type="org.dromara.business.domain.PolicyNews" id="PolicyNewsResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="newsTitle"    column="news_title"    />
        <result property="newsType"    column="news_type"    />
        <result property="newsContent"    column="news_content"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="sourceTitle"    column="source_title"    />
        <result property="policyCategory"    column="policy_category"    />
        <result property="policy2category"    column="policy_2category"    />
        <result property="fileType"    column="file_type"    />
        <result property="isThematicMeeting"    column="is_thematic_meeting"    />
        <result property="planType"    column="plan_type"    />
        <result property="belongPark"    column="belong_park"    />
        <result property="newsPosition"    column="news_position"    />
        <result property="fileTypeFunds"    column="file_type_funds"    />
        <result property="belongDistrict"    column="belong_district"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <!-- 查询上一条新闻 -->
    <select id="selectPreviousNews" resultType="org.dromara.business.domain.vo.PolicyNewsVo">
        SELECT id, news_title
        FROM neknd_policy_news
        WHERE news_type = #{newsType}
          AND id &lt; #{currentId}
          AND del_flag = '0'
          AND status = '0'
        ORDER BY id DESC
        LIMIT 1
    </select>

    <!-- 查询下一条新闻 -->
    <select id="selectNextNews" resultType="org.dromara.business.domain.vo.PolicyNewsVo">
        SELECT id, news_title
        FROM neknd_policy_news
        WHERE news_type = #{newsType}
          AND id &gt; #{currentId}
          AND del_flag = '0'
          AND status = '0'
        ORDER BY id ASC
        LIMIT 1
    </select>

</mapper>

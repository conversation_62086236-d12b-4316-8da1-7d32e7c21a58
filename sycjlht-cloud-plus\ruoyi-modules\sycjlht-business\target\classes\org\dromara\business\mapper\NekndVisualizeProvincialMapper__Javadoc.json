{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getParticipateData", "paramTypes": ["java.lang.String"], "doc": "\n 获取参加园区、企业、高校数\r\n"}, {"name": "getProvincialDynamicMonitoring", "paramTypes": ["java.lang.String"], "doc": "\n 市域动态监测\r\n"}, {"name": "getProvincialParkDistribution", "paramTypes": ["java.lang.String"], "doc": "\n 园区分布及产值 - 数量和产值\r\n"}, {"name": "getProvincialParkDistributionProjectCount", "paramTypes": ["java.lang.String"], "doc": "\n 园区分布及产值 - 产教融合项目数\r\n"}, {"name": "getProvincialPolicyLanding", "paramTypes": ["java.lang.String"], "doc": "\n 政策落地\r\n"}, {"name": "getKeyLeadingEnterprise", "paramTypes": ["java.lang.String"], "doc": "\n 重点企业排名\r\n"}, {"name": "getProvincialNationalLevelSchool", "paramTypes": ["java.lang.String"], "doc": "\n 各省水平院校数量分布\r\n"}, {"name": "getProvincialTalentTrainingConversionRate", "paramTypes": ["java.lang.String"], "doc": "\n 贯通培养人数转化率\r\n"}, {"name": "getProvincialSkillCertificate", "paramTypes": ["java.lang.String"], "doc": "\n 技能证书 - 技能等级证书数量\r\n"}, {"name": "getProvincialProfessionalSkillCertificate", "paramTypes": ["java.lang.String"], "doc": "\n 技能证书 - 专业技能等级证书数量\r\n"}, {"name": "getKeyHelpList", "paramTypes": ["java.lang.String"], "doc": "\n 重点帮扶清单\r\n"}], "constructors": []}
{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getDynamicMonitoringAllIndicatorResult", "paramTypes": ["java.util.List"], "doc": "\n 获取市域动态监测结果\r\n"}, {"name": "getParkDistribution", "paramTypes": ["java.util.List", "java.util.List"], "doc": "\n 获取园区分布及产值结果\r\n"}, {"name": "getProvincialPolicyLandingResult", "paramTypes": ["java.util.List"], "doc": "\n 政策落地\r\n"}, {"name": "getKeyLeadingEnterpriseResult", "paramTypes": ["java.util.List"], "doc": "\n 重点企业排名\r\n"}, {"name": "getProvincialLevelSchoolResult", "paramTypes": ["java.util.List"], "doc": "\n 获取各市水平院校数量分布\r\n"}, {"name": "getProvincialTalentTrainingConversionRateResutl", "paramTypes": ["java.util.List"], "doc": "\n 贯通人才培养转化率\r\n"}, {"name": "getSkillCertificate", "paramTypes": ["java.util.List", "java.util.List"], "doc": "\n 技能证书\r\n"}, {"name": "getKeyHelpListResult", "paramTypes": ["java.util.List"], "doc": "\n 重点帮扶清单\r\n"}], "constructors": []}
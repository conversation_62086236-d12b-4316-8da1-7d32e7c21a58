<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndCoursesVideosMapper">
    
    <resultMap type="NekndCoursesVideos" id="NekndCoursesVideosResult">
        <result property="videoId"    column="video_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="title"    column="title"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="duration"    column="duration"    />
    </resultMap>

    <sql id="selectNekndCoursesVideosVo">
        select video_id, course_id, title, video_url, duration from neknd_courses_videos
    </sql>

    <select id="selectNekndCoursesVideosList" parameterType="NekndCoursesVideos" resultMap="NekndCoursesVideosResult">
        <include refid="selectNekndCoursesVideosVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="courseId != null  and courseId != ''"> and course_id = #{courseId}</if>
        </where>
    </select>
    
    <select id="selectNekndCoursesVideosByVideoId" parameterType="Integer" resultMap="NekndCoursesVideosResult">
        <include refid="selectNekndCoursesVideosVo"/>
        where video_id = #{videoId}
    </select>
        
    <insert id="insertNekndCoursesVideos" parameterType="NekndCoursesVideos" useGeneratedKeys="true" keyProperty="videoId">
        insert into neknd_courses_videos
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseId != null">course_id,</if>
            <if test="title != null">title,</if>
            <if test="videoUrl != null">video_url,</if>
            <if test="duration != null">duration,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseId != null">#{courseId},</if>
            <if test="title != null">#{title},</if>
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="duration != null">#{duration},</if>
         </trim>
    </insert>

    <update id="updateNekndCoursesVideos" parameterType="NekndCoursesVideos">
        update neknd_courses_videos
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="duration != null">duration = #{duration},</if>
        </trim>
        where video_id = #{videoId}
    </update>

    <delete id="deleteNekndCoursesVideosByVideoId" parameterType="Integer">
        delete from neknd_courses_videos where video_id = #{videoId}
    </delete>

    <delete id="deleteNekndCoursesVideosByVideoIds" parameterType="String">
        delete from neknd_courses_videos where video_id in 
        <foreach item="videoId" collection="array" open="(" separator="," close=")">
            #{videoId}
        </foreach>
    </delete>
</mapper>
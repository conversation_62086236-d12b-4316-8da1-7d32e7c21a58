{"doc": "\n 公司和学校的员工申请记录Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-09-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "initProvincialNameToIdMap", "paramTypes": [], "doc": "\n 初始化省份名称到 ID 的映射\r\n"}, {"name": "selectNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询公司和学校的员工申请记录\r\n\r\n @param id 公司和学校的员工申请记录主键\r\n @return 公司和学校的员工申请记录\r\n"}, {"name": "selectNekndEmployeeApplicationList", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 查询公司和学校的员工申请记录列表\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 公司和学校的员工申请记录\r\n"}, {"name": "insertNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 新增公司和学校的员工申请记录\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 结果\r\n"}, {"name": "updateNekndEmployeeApplication", "paramTypes": ["org.dromara.business.domain.NekndEmployeeApplication"], "doc": "\n 修改公司和学校的员工申请记录\r\n\r\n @param nekndEmployeeApplication 公司和学校的员工申请记录\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployeeApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除公司和学校的员工申请记录\r\n\r\n @param ids 需要删除的公司和学校的员工申请记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndEmployeeApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除公司和学校的员工申请记录信息\r\n\r\n @param id 公司和学校的员工申请记录主键\r\n @return 结果\r\n"}, {"name": "checkUserAllowed", "paramTypes": ["org.dromara.system.domain.SysUser"], "doc": "\n 校验用户是否允许操作\r\n\r\n @param user 用户信息\r\n"}, {"name": "selectUserList", "paramTypes": ["org.dromara.system.domain.SysUser"], "doc": "\n 根据条件分页查询用户列表\r\n\r\n @param user 用户信息\r\n @return 用户信息集合信息\r\n"}, {"name": "checkUserDataScope", "paramTypes": ["java.lang.Long"], "doc": "\n 校验用户是否有数据权限\r\n\r\n @param userId 用户id\r\n"}, {"name": "insertUserRole", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 新增用户角色信息\r\n\r\n @param userId 用户ID\r\n @param roleIds 角色组\r\n"}, {"name": "insertUserAuth", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 用户授权角色\r\n\r\n @param userId 用户ID\r\n @param roleIds 角色组\r\n"}, {"name": "importUserApplication", "paramTypes": ["java.util.List", "java.lang.Bo<PERSON>an", "java.lang.Long", "java.lang.Long"], "doc": "\n 导入用户数据（已优化）\r\n\r\n @param personList 用户数据列表\r\n @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据\r\n @return 结果\r\n"}], "constructors": []}
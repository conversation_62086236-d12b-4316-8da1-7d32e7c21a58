package org.dromara.business.domain;

import io.github.linpeilie.AutoMapperConfig__489;
import io.github.linpeilie.BaseMapper;
import org.dromara.business.domain.bo.PolicyNewsBoToPolicyNewsMapper__11;
import org.dromara.business.domain.vo.PolicyNewsVo;
import org.dromara.business.domain.vo.PolicyNewsVoToPolicyNewsMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__489.class,
    uses = {PolicyNewsVoToPolicyNewsMapper__11.class,PolicyNewsBoToPolicyNewsMapper__11.class},
    imports = {}
)
public interface PolicyNewsToPolicyNewsVoMapper__11 extends BaseMapper<PolicyNews, PolicyNewsVo> {
}

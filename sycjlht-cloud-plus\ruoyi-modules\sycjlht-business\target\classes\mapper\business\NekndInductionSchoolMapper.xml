<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndInductionSchoolMapper">
    
    <resultMap type="NekndInductionSchool" id="NekndInductionSchoolResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="title"    column="title"    />
        <result property="info"    column="info"    />
        <result property="serverConcent"    column="server_concent"    />
        <result property="operationConcent"    column="operation_concent"    />
        <result property="experienceConcent"    column="experience_concent"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="model"    column="model"    />
        <result property="title_a"    column="title_a"    />
        <result property="title_b"    column="title_b"    />
        <result property="title_c"    column="title_c"    />
        <result property="schoolDeptId"    column="school_dept_id"    />
        <result property="collegeName"    column="college_name"    />
        <result property="cooperationType"    column="cooperation_type"    />
        <result property="collaborator"    column="collaborator"    />
    </resultMap>

    <sql id="selectNekndInductionSchoolVo">
        select id, cover_uri, title, info, server_concent, operation_concent, experience_concent, del_flag, create_by, create_time, update_by, update_time, model, title_a, title_b, title_c, school_dept_id, college_name, cooperation_type, collaborator from neknd_induction_school
    </sql>

    <select id="selectNekndInductionSchoolList" parameterType="NekndInductionSchool" resultMap="NekndInductionSchoolResult">
        <include refid="selectNekndInductionSchoolVo"/>
        <where>  
            <if test="coverUri != null  and coverUri != ''"> and cover_uri = #{coverUri}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="info != null  and info != ''"> and info = #{info}</if>
            <if test="serverConcent != null  and serverConcent != ''"> and server_concent = #{serverConcent}</if>
            <if test="operationConcent != null  and operationConcent != ''"> and operation_concent = #{operationConcent}</if>
            <if test="experienceConcent != null  and experienceConcent != ''"> and experience_concent = #{experienceConcent}</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="title_a != null  and title_a != ''"> and title_a = #{title_a}</if>
            <if test="title_b != null  and title_b != ''"> and title_b = #{title_b}</if>
            <if test="title_c != null  and title_c != ''"> and title_c = #{title_c}</if>
             <if test="schoolDeptId != null  and schoolDeptId != ''"> and school_dept_id = #{schoolDeptId}</if>
              <if test="collegeName != null  and collegeName != ''"> and college_name = #{collegeName}</if>
               <if test="cooperationType != null  and cooperationType != ''"> and cooperation_type = #{cooperationType}</if>
                <if test="collaborator != null  and collaborator != ''"> and collaborator = #{collaborator}</if>
        </where>
    </select>
    
    <select id="selectNekndInductionSchoolById" parameterType="Integer" resultMap="NekndInductionSchoolResult">
        <include refid="selectNekndInductionSchoolVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndInductionSchool" parameterType="NekndInductionSchool" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_induction_school
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">cover_uri,</if>
            <if test="title != null">title,</if>
            <if test="info != null">info,</if>
            <if test="serverConcent != null">server_concent,</if>
            <if test="operationConcent != null">operation_concent,</if>
            <if test="experienceConcent != null">experience_concent,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="model != null">model,</if>
            <if test="title_a != null">title_a,</if>
            <if test="title_b != null">title_b,</if>
            <if test="title_c != null">title_c,</if>
            <if test="schoolDeptId != null">school_dept_id,</if>
            <if test="collegeName != null">college_name,</if>
            <if test="cooperationType != null">cooperation_type,</if>
            <if test="collaborator != null">collaborator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">#{coverUri},</if>
            <if test="title != null">#{title},</if>
            <if test="info != null">#{info},</if>
            <if test="serverConcent != null">#{serverConcent},</if>
            <if test="operationConcent != null">#{operationConcent},</if>
            <if test="experienceConcent != null">#{experienceConcent},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="model != null">#{model},</if>
            <if test="title_a != null">#{title_a},</if>
            <if test="title_b != null">#{title_b},</if>
            <if test="title_c != null">#{title_c},</if>
            <if test="schoolDeptId != null">#{schoolDeptId},</if>
            <if test="collegeName != null">#{collegeName},</if>
            <if test="cooperationType != null">#{cooperationType},</if>
            <if test="collaborator != null">#{collaborator},</if>
         </trim>
    </insert>

    <update id="updateNekndInductionSchool" parameterType="NekndInductionSchool">
        update neknd_induction_school
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="title != null">title = #{title},</if>
            <if test="info != null">info = #{info},</if>
            <if test="serverConcent != null">server_concent = #{serverConcent},</if>
            <if test="operationConcent != null">operation_concent = #{operationConcent},</if>
            <if test="experienceConcent != null">experience_concent = #{experienceConcent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="model != null">model = #{model},</if>
            <if test="title_a != null">title_a = #{title_a},</if>
            <if test="title_b != null">title_b = #{title_b},</if>
            <if test="title_c != null">title_c = #{title_c},</if>
            <if test="schoolDeptId != null">school_dept_id = #{schoolDeptId},</if>
            <if test="collegeName != null">college_name = #{collegeName},</if>
            <if test="cooperationType != null">cooperation_type = #{cooperationType},</if>
            <if test="collaborator != null">collaborator = #{collaborator},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndInductionSchoolById" parameterType="Integer">
        delete from neknd_induction_school where id = #{id}
    </delete>

    <delete id="deleteNekndInductionSchoolByIds" parameterType="String">
        delete from neknd_induction_school where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 教育端个人中心图表1 -->
    <select id="selectCollegeStats" resultType="java.util.Map" parameterType="String">
        SELECT
        CASE
        WHEN college_name = 1 THEN '商学院'
        WHEN college_name = 2 THEN '工学院'
        WHEN college_name = 3 THEN '会计学院'
        WHEN college_name = 4 THEN '建筑学院'
        WHEN college_name = 5 THEN '健康管理学院'
        WHEN college_name = 6 THEN '人文艺术学院'
        WHEN college_name = 7 THEN '生物工程学院'
        WHEN college_name = 8 THEN '信息工程学院'
        WHEN college_name = 9 THEN '其他'
        END AS Name,
        COUNT(college_name) as count,college_name as collegeName,school_dept_id as schoolDeptId
        FROM neknd_induction_school
        WHERE college_name != "" AND college_name IS NOT NULL AND del_flag = 0 AND school_dept_id = #{id}
        GROUP BY college_name ORDER By count DESC
    </select>

</mapper>
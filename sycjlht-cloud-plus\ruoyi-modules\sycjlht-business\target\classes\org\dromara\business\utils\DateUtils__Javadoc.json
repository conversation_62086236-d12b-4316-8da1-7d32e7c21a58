{"doc": "\n 时间工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getNowDate", "paramTypes": [], "doc": "\n 获取当前Date型日期\r\n\r\n @return Date() 当前日期\r\n"}, {"name": "getDate", "paramTypes": [], "doc": "\n 获取当前日期, 默认格式为yyyy-MM-dd\r\n\r\n @return String\r\n"}, {"name": "datePath", "paramTypes": [], "doc": "\n 日期路径 即年/月/日 如2018/08/08\r\n"}, {"name": "dateTime", "paramTypes": [], "doc": "\n 日期路径 即年/月/日 如20180808\r\n"}, {"name": "parseDate", "paramTypes": ["java.lang.Object"], "doc": "\n 日期型字符串转化为日期 格式\r\n"}, {"name": "getServerStartDate", "paramTypes": [], "doc": "\n 获取服务器启动时间\r\n"}, {"name": "differentDaysByMillisecond", "paramTypes": ["java.util.Date", "java.util.Date"], "doc": "\n 计算相差天数\r\n"}, {"name": "timeDistance", "paramTypes": ["java.util.Date", "java.util.Date"], "doc": "\n 计算时间差\r\n\r\n @param endDate 最后时间\r\n @param startTime 开始时间\r\n @return 时间差（天/小时/分钟）\r\n"}, {"name": "toDate", "paramTypes": ["java.time.LocalDateTime"], "doc": "\n 增加 LocalDateTime ==> Date\r\n"}, {"name": "toDate", "paramTypes": ["java.time.LocalDate"], "doc": "\n 增加 LocalDate ==> Date\r\n"}], "constructors": []}
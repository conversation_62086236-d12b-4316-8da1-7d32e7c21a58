package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__489;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__489.class,
    uses = {SysOperLogBoToSysOperLogMapper__11.class,OperLogEventToSysOperLogBoMapper__11.class},
    imports = {}
)
public interface SysOperLogBoToOperLogEventMapper__11 extends BaseMapper<SysOperLogBo, OperLogEvent> {
}

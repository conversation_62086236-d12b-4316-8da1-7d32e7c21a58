{"doc": "\n 成员单位-园区Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2025-05-16\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUnitParkById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询成员单位-园区\r\n\r\n @param id 成员单位-园区主键\r\n @return 成员单位-园区\r\n"}, {"name": "selectNekndUnitParkByUid", "paramTypes": ["java.lang.Long"], "doc": "\n 根据单位id查询\r\n @param uid\r\n @return\r\n"}, {"name": "selectNekndUnitParkList", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": "\n 查询成员单位-园区列表\r\n\r\n @param nekndUnitPark 成员单位-园区\r\n @return 成员单位-园区集合\r\n"}, {"name": "insertNekndUnitPark", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": "\n 新增成员单位-园区\r\n\r\n @param nekndUnitPark 成员单位-园区\r\n @return 结果\r\n"}, {"name": "updateNekndUnitPark", "paramTypes": ["org.dromara.business.domain.NekndUnitPark"], "doc": "\n 修改成员单位-园区\r\n\r\n @param nekndUnitPark 成员单位-园区\r\n @return 结果\r\n"}, {"name": "deleteNekndUnitParkById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除成员单位-园区\r\n\r\n @param id 成员单位-园区主键\r\n @return 结果\r\n"}, {"name": "deleteNekndUnitParkByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除成员单位-园区\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
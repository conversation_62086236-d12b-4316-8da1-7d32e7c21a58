{"doc": " 政策新闻信息Service接口\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询政策新闻信息\n\n @param id 政策新闻信息主键\n @return 政策新闻信息\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询政策新闻信息列表\n\n @param bo 政策新闻信息\n @param pageQuery 分页查询条件\n @return 政策新闻信息集合\n"}, {"name": "queryList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 查询政策新闻信息列表\n\n @param bo 政策新闻信息\n @return 政策新闻信息集合\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 新增政策新闻信息\n\n @param bo 政策新闻信息\n @return 结果\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 修改政策新闻信息\n\n @param bo 政策新闻信息\n @return 结果\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除政策新闻信息信息\n\n @param ids 需要删除的政策新闻信息主键集合\n @param isValid 是否校验,true-删除前校验,false-不校验\n @return 结果\n"}], "constructors": []}
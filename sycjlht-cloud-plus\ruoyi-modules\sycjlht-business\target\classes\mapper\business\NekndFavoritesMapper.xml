<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndFavoritesMapper">
    
    <resultMap type="NekndFavorites" id="NekndFavoritesResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="targetId"    column="target_id"    />
        <result property="targetType"    column="target_type"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNekndFavoritesVo">
        select id, user_id, target_id, target_type, create_time from neknd_favorites
    </sql>

    <select id="selectNekndFavoritesList" parameterType="NekndFavorites" resultMap="NekndFavoritesResult">
        <include refid="selectNekndFavoritesVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="targetId != null "> and target_id = #{targetId}</if>
            <if test="targetType != null  and targetType != ''"> and target_type = #{targetType}</if>
        </where>
    </select>
    
    <select id="selectNekndFavoritesById" parameterType="Integer" resultMap="NekndFavoritesResult">
        <include refid="selectNekndFavoritesVo"/>
        where id = #{id}
    </select>
    <select id="getCountFavorite" resultType="java.lang.Integer">
        select count(1) from neknd_favorites where user_id = #{userId} and target_id = #{targetId} and target_type = #{targetType}
    </select>

    <insert id="insertNekndFavorites" parameterType="NekndFavorites" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_favorites
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="targetId != null">target_id,</if>
            <if test="targetType != null and targetType != ''">target_type,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="targetId != null">#{targetId},</if>
            <if test="targetType != null and targetType != ''">#{targetType},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateNekndFavorites" parameterType="NekndFavorites">
        update neknd_favorites
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="targetId != null">target_id = #{targetId},</if>
            <if test="targetType != null and targetType != ''">target_type = #{targetType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndFavoritesById" parameterType="Integer">
        delete from neknd_favorites where id = #{id}
    </delete>

    <delete id="deleteNekndFavoritesByIds" parameterType="String">
        delete from neknd_favorites where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
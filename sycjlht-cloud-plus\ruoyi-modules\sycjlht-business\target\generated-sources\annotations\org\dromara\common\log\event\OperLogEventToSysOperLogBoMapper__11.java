package org.dromara.common.log.event;

import io.github.linpeilie.AutoMapperConfig__489;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__11;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__11;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__489.class,
    uses = {SysOperLogBoToSysOperLogMapper__11.class,SysOperLogBoToOperLogEventMapper__11.class},
    imports = {}
)
public interface OperLogEventToSysOperLogBoMapper__11 extends BaseMapper<OperLogEvent, SysOperLogBo> {
}

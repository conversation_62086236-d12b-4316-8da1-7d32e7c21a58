{"doc": "\n Excel数据格式处理适配器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "format", "paramTypes": ["java.lang.Object", "java.lang.String[]", "org.apache.poi.ss.usermodel.Cell", "org.apache.poi.ss.usermodel.Workbook"], "doc": "\n 格式化\r\n\r\n @param value 单元格数据值\r\n @param args excel注解args参数组\r\n @param cell 单元格对象\r\n @param wb 工作簿对象\r\n\r\n @return 处理后的值\r\n"}], "constructors": []}
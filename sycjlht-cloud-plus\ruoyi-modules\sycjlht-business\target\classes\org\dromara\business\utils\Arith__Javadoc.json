{"doc": "\n 精确的浮点数运算\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DEF_DIV_SCALE", "doc": "默认除法运算精度 "}], "enumConstants": [], "methods": [{"name": "add", "paramTypes": ["double", "double"], "doc": "\n 提供精确的加法运算。\r\n @param v1 被加数\r\n @param v2 加数\r\n @return 两个参数的和\r\n"}, {"name": "sub", "paramTypes": ["double", "double"], "doc": "\n 提供精确的减法运算。\r\n @param v1 被减数\r\n @param v2 减数\r\n @return 两个参数的差\r\n"}, {"name": "mul", "paramTypes": ["double", "double"], "doc": "\n 提供精确的乘法运算。\r\n @param v1 被乘数\r\n @param v2 乘数\r\n @return 两个参数的积\r\n"}, {"name": "div", "paramTypes": ["double", "double"], "doc": "\n 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到\r\n 小数点以后10位，以后的数字四舍五入。\r\n @param v1 被除数\r\n @param v2 除数\r\n @return 两个参数的商\r\n"}, {"name": "div", "paramTypes": ["double", "double", "int"], "doc": "\n 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指\r\n 定精度，以后的数字四舍五入。\r\n @param v1 被除数\r\n @param v2 除数\r\n @param scale 表示表示需要精确到小数点以后几位。\r\n @return 两个参数的商\r\n"}, {"name": "round", "paramTypes": ["double", "int"], "doc": "\n 提供精确的小数位四舍五入处理。\r\n @param v 需要四舍五入的数字\r\n @param scale 小数点后保留几位\r\n @return 四舍五入后的结果\r\n"}, {"name": "avg", "paramTypes": ["java.util.List", "int"], "doc": "\n 提供精确的平均值运算。\r\n @param values 需要计算平均值的数值列表\r\n @param scale 表示需要精确到小数点以后几位。\r\n @return 平均值\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "这个类不能实例化 "}]}
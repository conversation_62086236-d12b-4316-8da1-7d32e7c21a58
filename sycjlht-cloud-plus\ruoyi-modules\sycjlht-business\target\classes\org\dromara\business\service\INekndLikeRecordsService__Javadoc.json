{"doc": "\n 用户点赞记录Service接口\r\n\r\n <AUTHOR>\r\n @date 2025-04-03\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndLikeRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询用户点赞记录\r\n\r\n @param id 用户点赞记录主键\r\n @return 用户点赞记录\r\n"}, {"name": "selectNekndLikeRecordsList", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": "\n 查询用户点赞记录列表\r\n\r\n @param nekndLikeRecords 用户点赞记录\r\n @return 用户点赞记录集合\r\n"}, {"name": "insertNekndLikeRecords", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": "\n 新增用户点赞记录\r\n\r\n @param nekndLikeRecords 用户点赞记录\r\n @return 结果\r\n"}, {"name": "updateNekndLikeRecords", "paramTypes": ["org.dromara.business.domain.NekndLikeRecords"], "doc": "\n 修改用户点赞记录\r\n\r\n @param nekndLikeRecords 用户点赞记录\r\n @return 结果\r\n"}, {"name": "deleteNekndLikeRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除用户点赞记录\r\n\r\n @param ids 需要删除的用户点赞记录主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndLikeRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除用户点赞记录信息\r\n\r\n @param id 用户点赞记录主键\r\n @return 结果\r\n"}], "constructors": []}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndEmployeeApplicationMapper">
    
    <resultMap type="NekndEmployeeApplication" id="NekndEmployeeApplicationResult">
        <result property="id"    column="id"    />
        <result property="pictureUri"    column="picture_uri"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="educationStatus"    column="education_status"    />
        <result property="sex"    column="sex"    />
        <result property="address"    column="address"    />
        <result property="phone"    column="phone"    />
        <result property="email"    column="email"    />
        <result property="workYear"    column="work_year"    />
        <result property="technical"    column="technical"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="status"    column="status"    />
        <result property="reviewStatus"    column="review_status"    />
    </resultMap>

    <sql id="selectNekndEmployeeApplicationVo">
        select id, picture_uri, user_id, name, education_status, sex, address, phone, email, work_year, technical, dept_id, dept_name, status, review_status from neknd_employee_application
    </sql>

    <select id="selectNekndEmployeeApplicationList" parameterType="NekndEmployeeApplication" resultMap="NekndEmployeeApplicationResult">
        <include refid="selectNekndEmployeeApplicationVo"/>
        <where>  
            <if test="pictureUri != null  and pictureUri != ''"> and picture_uri = #{pictureUri}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="educationStatus != null  and educationStatus != ''"> and education_status = #{educationStatus}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="reviewStatus != null  and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="reviewStatus != null and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectNekndEmployeeApplicationById" parameterType="Integer" resultMap="NekndEmployeeApplicationResult">
        <include refid="selectNekndEmployeeApplicationVo"/>
        where id = #{id}
    </select>
    <select id="selectIsEmployeeApplication" resultType="java.lang.Integer">
        select count(1) from neknd_employee_application where user_id = #{userId} and dept_id = #{deptId} and status = #{status}
    </select>

    <select id="selectCompanyNameByDeptId" resultType="String">
        SELECT company_name
        FROM neknd_company
        WHERE dept_id= #{deptId}
    </select>


    <insert id="insertNekndEmployeeApplication" parameterType="NekndEmployeeApplication" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_employee_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pictureUri != null">picture_uri,</if>
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="educationStatus != null">education_status,</if>
            <if test="sex != null">sex,</if>
            <if test="address != null">address,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="workYear != null">work_year,</if>
            <if test="technical != null">technical,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="status != null">status,</if>
            <if test="reviewStatus != null">review_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pictureUri != null">#{pictureUri},</if>
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="educationStatus != null">#{educationStatus},</if>
            <if test="sex != null">#{sex},</if>
            <if test="address != null">#{address},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="workYear != null">#{workYear},</if>
            <if test="technical != null">#{technical},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="status != null">#{status},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
         </trim>
    </insert>

    <update id="updateNekndEmployeeApplication" parameterType="NekndEmployeeApplication">
        update neknd_employee_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="pictureUri != null">picture_uri = #{pictureUri},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="educationStatus != null">education_status = #{educationStatus},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="address != null">address = #{address},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="workYear != null">work_year = #{workYear},</if>
            <if test="technical != null">technical = #{technical},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndEmployeeApplicationById" parameterType="Integer">
        delete from neknd_employee_application where id = #{id}
    </delete>

    <delete id="deleteNekndEmployeeApplicationByIds" parameterType="String">
        delete from neknd_employee_application where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectNekndEmployeeApplicationByIdCount" parameterType="Long" resultType="Long">
        SELECT COUNT(*)
        FROM neknd_employee_application
        where user_id = #{userId}
    </select>
</mapper>
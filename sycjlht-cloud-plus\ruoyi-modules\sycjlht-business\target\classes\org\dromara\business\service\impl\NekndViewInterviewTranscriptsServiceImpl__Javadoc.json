{"doc": "\n 面试记录查看Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-06-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndViewInterviewTranscriptsById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询面试记录查看\r\n\r\n @param id 面试记录查看主键\r\n @return 面试记录查看\r\n"}, {"name": "selectNekndViewInterviewTranscriptsList", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": "\n 查询面试记录查看列表\r\n\r\n @param nekndViewInterviewTranscripts 面试记录查看\r\n @return 面试记录查看\r\n"}, {"name": "insertNekndViewInterviewTranscripts", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": "\n 新增面试记录查看\r\n\r\n @param id 面试记录查看\r\n @return 结果\r\n"}, {"name": "updateNekndViewInterviewTranscripts", "paramTypes": ["org.dromara.business.domain.NekndViewInterviewTranscripts"], "doc": "\n 修改面试记录查看\r\n\r\n @param nekndViewInterviewTranscripts 面试记录查看\r\n @return 结果\r\n"}, {"name": "deleteNekndViewInterviewTranscriptsByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除面试记录查看\r\n\r\n @param ids 需要删除的面试记录查看主键\r\n @return 结果\r\n"}, {"name": "deleteNekndViewInterviewTranscriptsById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除面试记录查看信息\r\n\r\n @param id 面试记录查看主键\r\n @return 结果\r\n"}], "constructors": []}
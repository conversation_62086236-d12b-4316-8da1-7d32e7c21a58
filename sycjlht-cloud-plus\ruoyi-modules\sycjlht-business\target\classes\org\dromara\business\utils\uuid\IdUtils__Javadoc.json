{"doc": "\n ID生成器工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "randomUUID", "paramTypes": [], "doc": "\n 获取随机UUID\r\n\r\n @return 随机UUID\r\n"}, {"name": "simpleUUID", "paramTypes": [], "doc": "\n 简化的UUID，去掉了横线\r\n\r\n @return 简化的UUID，去掉了横线\r\n"}, {"name": "fastUUID", "paramTypes": [], "doc": "\n 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID\r\n\r\n @return 随机UUID\r\n"}, {"name": "fastSimpleUUID", "paramTypes": [], "doc": "\n 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID\r\n\r\n @return 简化的UUID，去掉了横线\r\n"}], "constructors": []}
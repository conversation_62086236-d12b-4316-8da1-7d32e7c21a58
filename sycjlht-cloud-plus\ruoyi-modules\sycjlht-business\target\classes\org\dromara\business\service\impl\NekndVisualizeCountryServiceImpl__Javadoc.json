{"doc": "", "fields": [], "enumConstants": [], "methods": [{"name": "getBasicData", "paramTypes": [], "doc": "\n 获取基础数据\r\n @return\r\n"}, {"name": "parseSpecialtyCoverageMatchRateMap", "paramTypes": ["java.util.List"], "doc": "\n 获取专业覆盖率匹配率\r\n @param specialtyCoverageMatchRate\r\n @return\r\n"}, {"name": "getProvincialLocalEmploymentRateResult", "paramTypes": ["java.util.List"], "doc": "\n 获取各省园区本地就业率\r\n"}, {"name": "getProvincialCountryLevelParkResult", "paramTypes": ["java.util.List", "java.util.List", "java.util.List"], "doc": "\n 各省国家级数量\r\n"}, {"name": "getProvincialEnterpriseTypeResult", "paramTypes": ["java.util.List"], "doc": "\n  各省企业类型数量分布\r\n"}, {"name": "getProvincialComprehensiveRankingResult", "paramTypes": ["java.util.List"], "doc": "\n 各省综合排名\r\n"}, {"name": "getIndustryEducationBenchmarkResult", "paramTypes": ["java.util.List"], "doc": "\n 专项冠军 - 产教融合标杆\r\n"}, {"name": "getMaximumParkResult", "paramTypes": ["java.util.List"], "doc": "\n 专项冠军 - 资源投入最大园区\r\n"}, {"name": "getFinancialSupportResult", "paramTypes": ["java.util.List"], "doc": "\n 专项冠军 - 财政扶持\r\n"}, {"name": "getFeatureLeadingParkResult", "paramTypes": ["java.util.List"], "doc": "\n 专项冠军 - 特色产业园区\r\n"}, {"name": "getKeyLeadingEnterpriseAndSchoolResult", "paramTypes": ["java.util.List"], "doc": "\n 专项冠军 - 重点牵头企业和学校\r\n"}, {"name": "getMaxCountryLevelProvincial", "paramTypes": ["java.util.List"], "doc": "\n 专项冠军 - 国家级\r\n"}, {"name": "getSchoolEnterpriseCooperationResult", "paramTypes": ["java.util.List"], "doc": "\n 专项冠军 - 校企合作转化\r\n"}, {"name": "getWarningPanelResult", "paramTypes": ["java.util.List", "java.util.List"], "doc": "\n 预警面板\r\n"}], "constructors": []}
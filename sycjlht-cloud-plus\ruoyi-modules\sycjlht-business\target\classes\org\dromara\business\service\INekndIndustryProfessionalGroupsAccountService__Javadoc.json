{"doc": "\n 【请填写功能名称】Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-25\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndIndustryProfessionalGroupsAccountByIndustryId", "paramTypes": ["java.lang.Long"], "doc": "\n 查询【请填写功能名称】\r\n\r\n @param industryId 【请填写功能名称】主键\r\n @return 【请填写功能名称】\r\n"}, {"name": "selectNekndIndustryProfessionalGroupsAccountList", "paramTypes": ["org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": "\n 查询【请填写功能名称】列表\r\n\r\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\r\n @return 【请填写功能名称】集合\r\n"}, {"name": "insertNekndIndustryProfessionalGroupsAccount", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.Long", "org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": "\n 新增【请填写功能名称】\r\n\r\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\r\n @return 结果\r\n"}, {"name": "updateNekndIndustryProfessionalGroupsAccount", "paramTypes": ["org.dromara.business.domain.NekndIndustryProfessionalGroupsAccount"], "doc": "\n 修改【请填写功能名称】\r\n\r\n @param nekndIndustryProfessionalGroupsAccount 【请填写功能名称】\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryProfessionalGroupsAccountByIndustryIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除【请填写功能名称】\r\n\r\n @param industryIds 需要删除的【请填写功能名称】主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndIndustryProfessionalGroupsAccountByIndustryId", "paramTypes": ["java.lang.Long"], "doc": "\n 删除【请填写功能名称】信息\r\n\r\n @param industryId 【请填写功能名称】主键\r\n @return 结果\r\n"}], "constructors": []}
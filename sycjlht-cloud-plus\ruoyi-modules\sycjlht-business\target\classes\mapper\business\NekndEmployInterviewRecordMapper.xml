<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndEmployInterviewRecordMapper">
    
    <resultMap type="NekndEmployInterviewRecord" id="NekndEmployInterviewRecordResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="employId"    column="employ_id"    />
        <result property="status"    column="status"    />
        <result property="content"    column="content"    />
        <result property="deptName"    column="dept_name"    />
        <result property="userName"    column="user_name"    />
        <result property="jobName"    column="job_name"    />
    </resultMap>

    <sql id="selectNekndEmployInterviewRecordVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, dept_id, user_id, employ_id, status, content, dept_name, user_name, job_name from neknd_employ_interview_record
    </sql>

    <select id="selectNekndEmployInterviewRecordList" parameterType="NekndEmployInterviewRecord" resultMap="NekndEmployInterviewRecordResult">
        <include refid="selectNekndEmployInterviewRecordVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="employId != null "> and employ_id = #{employId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="jobName != null  and jobName != ''"> and job_name like concat('%', #{jobName}, '%')</if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectNekndEmployInterviewRecordById" parameterType="Integer" resultMap="NekndEmployInterviewRecordResult">
        <include refid="selectNekndEmployInterviewRecordVo"/>
        where id = #{id}
    </select>
<!--    <select id="getlist" resultType="map">-->
<!--        select a.id id, a.dept_id dept_id,a.employ_id employ_id, a.`status` status,a.dept_name dept_name,a.job_name job_name,b.job_flag job_flag,b.job_salary job_salary-->
<!--        from neknd_employ_interview_record a left join  neknd_employ b on a.employ_id = b.id-->
<!--        WHERE-->
<!--        <where>-->
<!--            a.user_id = #{userId}-->
<!--            <if test="status = 1">and a.status = #{status}</if>-->
<!--        </where>-->
<!--        GROUP BY dept_id;-->
<!--    </select>-->

<!--    <select id="getStatus" resultType="java.lang.String">-->
<!--        select a.status-->
<!--        from neknd_employ_interview_record a left join  neknd_employ b on a.employ_id = b.id-->
<!--        <where>-->
<!--            <if test="userId != null || userId !=''"> a.user_id = #{userId}</if>-->
<!--        </where>-->
<!--    </select>-->


    <insert id="insertNekndEmployInterviewRecord" parameterType="NekndEmployInterviewRecord" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_employ_interview_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="employId != null">employ_id,</if>
            <if test="status != null">status,</if>
            <if test="content != null">content,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="userName != null">user_name,</if>
            <if test="jobName != null">job_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="employId != null">#{employId},</if>
            <if test="status != null">#{status},</if>
            <if test="content != null">#{content},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="userName != null">#{userName},</if>
            <if test="jobName != null">#{jobName},</if>
         </trim>
    </insert>

    <update id="updateNekndEmployInterviewRecord" parameterType="NekndEmployInterviewRecord">
        update neknd_employ_interview_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="employId != null">employ_id = #{employId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="content != null">content = #{content},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="jobName != null">job_name = #{jobName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndEmployInterviewRecordById" parameterType="Integer">
        delete from neknd_employ_interview_record where id = #{id}
    </delete>

    <delete id="deleteNekndEmployInterviewRecordByIds" parameterType="String">
        delete from neknd_employ_interview_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
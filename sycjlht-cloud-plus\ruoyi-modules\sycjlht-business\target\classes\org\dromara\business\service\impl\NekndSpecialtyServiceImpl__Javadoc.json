{"doc": "\n 专业信息Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-11-06\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询专业信息\r\n\r\n @param id 专业信息主键\r\n @return 专业信息\r\n"}, {"name": "selectNekndSpecialtyList", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 查询专业信息列表\r\n\r\n @param nekndSpecialty 专业信息\r\n @return 专业信息\r\n"}, {"name": "insertNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 新增专业信息\r\n\r\n @param nekndSpecialty 专业信息\r\n @return 结果\r\n"}, {"name": "updateNekndSpecialty", "paramTypes": ["org.dromara.business.domain.NekndSpecialty"], "doc": "\n 修改专业信息\r\n\r\n @param nekndSpecialty 专业信息\r\n @return 结果\r\n"}, {"name": "deleteNekndSpecialtyByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除专业信息\r\n\r\n @param ids 需要删除的专业信息主键\r\n @return 结果\r\n"}, {"name": "deleteNekndSpecialtyById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除专业信息信息\r\n\r\n @param id 专业信息主键\r\n @return 结果\r\n"}], "constructors": []}
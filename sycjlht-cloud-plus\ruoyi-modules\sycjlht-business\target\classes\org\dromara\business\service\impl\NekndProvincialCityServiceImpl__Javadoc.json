{"doc": "\n 市级Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndProvincialCityByCid", "paramTypes": ["java.lang.Long"], "doc": "\n 查询市级\r\n\r\n @param cid 市级主键\r\n @return 市级\r\n"}, {"name": "selectNekndProvincialCityList", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": "\n 查询市级列表\r\n\r\n @param nekndProvincialCity 市级\r\n @return 市级\r\n"}, {"name": "insertNekndProvincialCity", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": "\n 新增市级\r\n\r\n @param nekndProvincialCity 市级\r\n @return 结果\r\n"}, {"name": "updateNekndProvincialCity", "paramTypes": ["org.dromara.business.domain.NekndProvincialCity"], "doc": "\n 修改市级\r\n\r\n @param nekndProvincialCity 市级\r\n @return 结果\r\n"}, {"name": "deleteNekndProvincialCityByCids", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除市级\r\n\r\n @param cids 需要删除的市级主键\r\n @return 结果\r\n"}, {"name": "deleteNekndProvincialCityByCid", "paramTypes": ["java.lang.Long"], "doc": "\n 删除市级信息\r\n\r\n @param cid 市级主键\r\n @return 结果\r\n"}], "constructors": []}
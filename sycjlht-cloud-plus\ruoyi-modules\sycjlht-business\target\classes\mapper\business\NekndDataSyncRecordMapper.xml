<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndDataSyncRecordMapper">
    
    <resultMap type="NekndDataSyncRecord" id="NekndDataSyncRecordResult">
        <result property="id"    column="id"    />
        <result property="syncType"    column="sync_type"    />
        <result property="log"    column="log"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNekndDataSyncRecordVo">
        select id, sync_type, log, create_time from neknd_data_sync_record
    </sql>

    <select id="selectNekndDataSyncRecordList" parameterType="NekndDataSyncRecord" resultMap="NekndDataSyncRecordResult">
        <include refid="selectNekndDataSyncRecordVo"/>
        <where>  
            <if test="syncType != null  and syncType != ''"> and sync_type = #{syncType}</if>
            <if test="log != null  and log != ''"> and log = #{log}</if>
        </where>
    </select>
    
    <select id="selectNekndDataSyncRecordById" parameterType="Long" resultMap="NekndDataSyncRecordResult">
        <include refid="selectNekndDataSyncRecordVo"/>
        where id = #{id}
    </select>
    <select id="getLastSyncTime" resultType="java.util.Date">
        select create_time from neknd_data_sync_record where sync_type = #{syncType} order by create_time desc limit 1
    </select>

    <insert id="insertNekndDataSyncRecord" parameterType="NekndDataSyncRecord" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_data_sync_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="syncType != null and syncType != ''">sync_type,</if>
            <if test="log != null and log != ''">log,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="syncType != null and syncType != ''">#{syncType},</if>
            <if test="log != null and log != ''">#{log},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateNekndDataSyncRecord" parameterType="NekndDataSyncRecord">
        update neknd_data_sync_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="syncType != null and syncType != ''">sync_type = #{syncType},</if>
            <if test="log != null">log = #{log},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndDataSyncRecordById" parameterType="Long">
        delete from neknd_data_sync_record where id = #{id}
    </delete>

    <delete id="deleteNekndDataSyncRecordByIds" parameterType="String">
        delete from neknd_data_sync_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
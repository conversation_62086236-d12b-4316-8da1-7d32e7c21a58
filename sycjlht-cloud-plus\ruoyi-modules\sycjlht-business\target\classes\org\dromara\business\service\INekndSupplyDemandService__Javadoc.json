{"doc": "\n 项目需求Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSupplyDemandById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询项目需求\r\n\r\n @param id 项目需求主键\r\n @return 项目需求\r\n"}, {"name": "selectNekndSupplyDemandList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 查询项目需求列表\r\n\r\n @param nekndSupplyDemand 项目需求\r\n @return 项目需求集合\r\n"}, {"name": "selectDemandList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 后台查询项目需求列表\r\n\r\n @param nekndSupplyDemand 项目需求\r\n @return 项目需求集合\r\n"}, {"name": "insertNekndSupplyDemand", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 新增项目需求\r\n\r\n @param nekndSupplyDemand 项目需求\r\n @return 结果\r\n"}, {"name": "updateNekndSupplyDemand", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 修改项目需求\r\n\r\n @param nekndSupplyDemand 项目需求\r\n @return 结果\r\n"}, {"name": "deleteNekndSupplyDemandByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除项目需求\r\n\r\n @param ids 需要删除的项目需求主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndSupplyDemandById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除项目需求信息\r\n\r\n @param id 项目需求主键\r\n @return 结果\r\n"}, {"name": "upDockingtatus", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 根据需求id修改对接状态(0:待对接,1:已对接,2:已结束)\r\n\r\n"}, {"name": "selectAuditDemandList", "paramTypes": ["org.dromara.business.domain.NekndSupplyDemand"], "doc": "\n 查询审核项目需求列表\r\n @param nekndSupplyDemand\r\n @return\r\n"}], "constructors": []}
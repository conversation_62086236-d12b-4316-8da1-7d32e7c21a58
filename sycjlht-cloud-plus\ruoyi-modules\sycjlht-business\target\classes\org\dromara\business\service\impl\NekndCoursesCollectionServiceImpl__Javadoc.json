{"doc": "\n 课程收藏记录Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesCollectionById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询课程收藏记录\r\n\r\n @param id 课程收藏记录主键\r\n @return 课程收藏记录\r\n"}, {"name": "selectNekndCoursesCollectionList", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 查询课程收藏记录列表\r\n\r\n @param nekndCoursesCollection 课程收藏记录\r\n @return 课程收藏记录\r\n"}, {"name": "insertNekndCoursesCollection", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 新增课程收藏记录\r\n\r\n @param nekndCoursesCollection 课程收藏记录\r\n @return 结果\r\n"}, {"name": "updateNekndCoursesCollection", "paramTypes": ["org.dromara.business.domain.NekndCoursesCollection"], "doc": "\n 修改课程收藏记录\r\n\r\n @param nekndCoursesCollection 课程收藏记录\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesCollectionByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除课程收藏记录\r\n\r\n @param ids 需要删除的课程收藏记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesCollectionById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除课程收藏记录信息\r\n\r\n @param id 课程收藏记录主键\r\n @return 结果\r\n"}], "constructors": []}
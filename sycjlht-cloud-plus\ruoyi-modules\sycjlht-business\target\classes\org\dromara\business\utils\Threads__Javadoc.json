{"doc": "\n 线程相关工具类.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "sleep", "paramTypes": ["long"], "doc": "\n sleep等待,单位为毫秒\r\n"}, {"name": "shutdownAndAwaitTermination", "paramTypes": ["java.util.concurrent.ExecutorService"], "doc": "\n 停止线程池\r\n 先使用shutdown, 停止接收新任务并尝试完成所有已存在任务.\r\n 如果超时, 则调用shutdownNow, 取消在workQueue中Pending的任务,并中断所有阻塞函数.\r\n 如果仍然超時，則強制退出.\r\n 另对在shutdown时线程本身被调用中断做了处理.\r\n"}, {"name": "printException", "paramTypes": ["java.lang.Runnable", "java.lang.Throwable"], "doc": "\n 打印线程异常信息\r\n"}], "constructors": []}
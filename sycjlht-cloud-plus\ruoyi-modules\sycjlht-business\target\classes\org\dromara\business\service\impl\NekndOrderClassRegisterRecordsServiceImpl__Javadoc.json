{"doc": "\n 订单班报名记录Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-08-28\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndOrderClassRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询订单班报名记录\r\n\r\n @param id 订单班报名记录主键\r\n @return 订单班报名记录\r\n"}, {"name": "selectNekndOrderClassRegisterRecordsList", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": "\n 查询订单班报名记录列表\r\n\r\n @param nekndOrderClassRegisterRecords 订单班报名记录\r\n @return 订单班报名记录\r\n"}, {"name": "insertNekndOrderClassRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": "\n 新增订单班报名记录\r\n\r\n @param nekndOrderClassRegisterRecords 订单班报名记录\r\n @return 结果\r\n"}, {"name": "updateNekndOrderClassRegisterRecords", "paramTypes": ["org.dromara.business.domain.NekndOrderClassRegisterRecords"], "doc": "\n 修改订单班报名记录\r\n\r\n @param nekndOrderClassRegisterRecords 订单班报名记录\r\n @return 结果\r\n"}, {"name": "deleteNekndOrderClassRegisterRecordsByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除订单班报名记录\r\n\r\n @param ids 需要删除的订单班报名记录主键\r\n @return 结果\r\n"}, {"name": "deleteNekndOrderClassRegisterRecordsById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除订单班报名记录信息\r\n\r\n @param id 订单班报名记录主键\r\n @return 结果\r\n"}], "constructors": []}
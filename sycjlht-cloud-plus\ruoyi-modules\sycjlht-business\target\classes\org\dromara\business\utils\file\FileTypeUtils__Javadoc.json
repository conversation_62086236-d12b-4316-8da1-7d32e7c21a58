{"doc": "\n 文件类型工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getFileType", "paramTypes": ["java.io.File"], "doc": "\n 获取文件类型\r\n <p>\r\n 例如: ruoyi.txt, 返回: txt\r\n\r\n @param file 文件名\r\n @return 后缀（不含\".\")\r\n"}, {"name": "getFileType", "paramTypes": ["java.lang.String"], "doc": "\n 获取文件类型\r\n <p>\r\n 例如: ruoyi.txt, 返回: txt\r\n\r\n @param fileName 文件名\r\n @return 后缀（不含\".\")\r\n"}, {"name": "getFileExtendName", "paramTypes": ["byte[]"], "doc": "\n 获取文件类型\r\n\r\n @param photoByte 文件字节码\r\n @return 后缀（不含\".\")\r\n"}], "constructors": []}
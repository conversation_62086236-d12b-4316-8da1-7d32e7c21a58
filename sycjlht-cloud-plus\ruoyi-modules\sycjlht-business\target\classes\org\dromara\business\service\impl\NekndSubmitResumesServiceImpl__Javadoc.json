{"doc": "\n 查看投递简历Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSubmitResumesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询查看投递简历\r\n\r\n @param id 查看投递简历主键\r\n @return 查看投递简历\r\n"}, {"name": "selectNekndSubmitResumesList", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": "\n 查询查看投递简历列表\r\n\r\n @param nekndSubmitResumes 查看投递简历\r\n @return 查看投递简历\r\n"}, {"name": "insertNekndSubmitResumes", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": "\n 新增查看投递简历\r\n\r\n @param nekndSubmitResumes 查看投递简历\r\n @return 结果\r\n"}, {"name": "updateNekndSubmitResumes", "paramTypes": ["org.dromara.business.domain.NekndSubmitResumes"], "doc": "\n 修改查看投递简历\r\n\r\n @param nekndSubmitResumes 查看投递简历\r\n @return 结果\r\n"}, {"name": "deleteNekndSubmitResumesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除查看投递简历\r\n\r\n @param ids 需要删除的查看投递简历主键\r\n @return 结果\r\n"}, {"name": "deleteNekndSubmitResumesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除查看投递简历信息\r\n\r\n @param id 查看投递简历主键\r\n @return 结果\r\n"}], "constructors": []}
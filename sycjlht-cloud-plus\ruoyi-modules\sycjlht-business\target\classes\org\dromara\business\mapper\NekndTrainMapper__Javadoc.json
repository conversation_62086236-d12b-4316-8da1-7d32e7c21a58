{"doc": "\n 培训(证书/活动/项目)Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询培训(证书/活动/项目)\r\n\r\n @param id 培训(证书/活动/项目)主键\r\n @return 培训(证书/活动/项目)\r\n"}, {"name": "selectNekndTrainList", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": "\n 查询培训(证书/活动/项目)列表\r\n\r\n @param nekndTrain 培训(证书/活动/项目)\r\n @return 培训(证书/活动/项目)集合\r\n"}, {"name": "insertNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": "\n 新增培训(证书/活动/项目)\r\n\r\n @param nekndTrain 培训(证书/活动/项目)\r\n @return 结果\r\n"}, {"name": "updateNekndTrain", "paramTypes": ["org.dromara.business.domain.NekndTrain"], "doc": "\n 修改培训(证书/活动/项目)\r\n\r\n @param nekndTrain 培训(证书/活动/项目)\r\n @return 结果\r\n"}, {"name": "deleteNekndTrainById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除培训(证书/活动/项目)\r\n\r\n @param id 培训(证书/活动/项目)主键\r\n @return 结果\r\n"}, {"name": "deleteNekndTrainByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除培训(证书/活动/项目)\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}, {"name": "selectState", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询是否为线上课程\r\n @param id\r\n @return\r\n"}, {"name": "selectTrainDetail", "paramTypes": ["java.lang.Integer", "java.lang.String"], "doc": "\n 查询培训详情\r\n @param id\r\n @param status\r\n @return\r\n"}], "constructors": []}
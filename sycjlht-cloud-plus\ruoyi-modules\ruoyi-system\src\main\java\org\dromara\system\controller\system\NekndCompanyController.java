package org.dromara.system.controller.system;

import org.dromara.business.annotation.Anonymous;
import org.dromara.business.annotation.Log;
import org.dromara.business.annotation.RepeatSubmit;
import org.dromara.business.constant.CacheConstants;
import org.dromara.business.constant.Constants;
import org.dromara.common.web.core.BaseController;
import org.dromara.business.domain.AjaxResult;
import org.dromara.system.domain.SysDept;
import org.dromara.system.domain.SysUser;
import org.dromara.system.api.model.LoginUser;
import org.dromara.business.core.page.TableDataInfo;
import org.dromara.business.core.redis.RedisCache;
import org.dromara.business.enums.BusinessType;
import org.dromara.business.utils.CustomPageUtils;
import org.dromara.business.utils.MessageUtils;
import org.dromara.business.utils.SecurityUtils;
import org.dromara.business.utils.StringUtils;
import org.dromara.business.utils.map.MapUtils;
import org.dromara.business.utils.poi.ExcelUtil;
import org.dromara.business.manager.AsyncManager;
import org.dromara.business.manager.factory.AsyncFactory;
import org.dromara.system.domain.NekndCompany;
import org.dromara.business.domain.NekndEmploy;
import org.dromara.system.domain.NekndServiceRequirements;
import org.dromara.system.domain.NekndSupplyDemand;
import org.dromara.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 企业信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-08
 */
@RestController
@RequestMapping("/system/company")
public class NekndCompanyController extends BaseController {
    @Autowired
    private INekndCompanyService nekndCompanyService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private INekndEmployService nekndEmployService;
    @Autowired
    private INekndServiceRequirementsService nekndServiceRequirementsService;
    @Autowired
    private INekndSupplyDemandService nekndSupplyDemandService;

    /**
     * 查询企业信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:company:list')")
    @GetMapping("/list")
    public TableDataInfo list(NekndCompany nekndCompany) {
        // Long deptId = getLoginUser().getDeptId();
        LoginUser loginUser = getLoginUser();
        if (loginUser == null)
            return getDataTable(new ArrayList<>());
        SysUser user = loginUser.getUser();

        // 校验当前用户是否为或者教育端角色，
        // 1.如果是企业端或者教育端则只显示自己的企业信息，不能查询其他人
        List<Long> roleList = sysRoleService.selectRoleListByUserId(user.getUserId());
        if (roleList.size() == 0)
            return getDataTable(new ArrayList<>());
        if (roleList.contains(4L) || roleList.contains(5L) || roleList.contains(6L)) {
            if (user.getDeptId() == null)
                return getDataTable(new ArrayList<>());
            nekndCompany.setDeptId(user.getDeptId());
        }
        // 人社局
        if (roleList.contains(7L)) {
            if (user.getUserName() == null)
                return getDataTable(new ArrayList<>());
            nekndCompany.setCreateBy(user.getUserName());
        }
        startPage();
        List<NekndCompany> list = nekndCompanyService.selectNekndCompanyList(nekndCompany);
        return getDataTable(list);
    }

    /**
     * 导出企业信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:company:export')")
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NekndCompany nekndCompany) {
        List<NekndCompany> list = nekndCompanyService.selectNekndCompanyList(nekndCompany);
        ExcelUtil<NekndCompany> util = new ExcelUtil<NekndCompany>(NekndCompany.class);
        util.exportExcel(response, list, "企业信息数据");
    }

    /**
     * 获取企业信息详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:company:query')")
    @GetMapping(value = "/{id}")
    @Anonymous
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(nekndCompanyService.selectNekndCompanyById(id));
    }

    /**
     * 新增企业信息
     */
    @PreAuthorize("@ss.hasPermi('system:company:add')")
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody NekndCompany nekndCompany) {

        System.out.println("新增企业信息");
        Long deptId = getLoginUser().getDeptId();
        if (deptId != null && deptId != 107) {
            // if(deptId==107){
            // return warn("您不是企业用户！");
            // }
            List<SysUser> sysUsers = sysUserService.selectUserByDeptId(deptId);
            String createBy = sysUsers.get(0).getCreateBy();

            if (StringUtils.isNotEmpty(createBy)) {
                // 后台生成的账号,更新认证操作
                LoginUser loginUser = getLoginUser();
                if (loginUser == null)
                    return error("获取当前用户失败！");
                SysUser user = loginUser.getUser();

                // 校验当前用户是否为企业端或者教育端角色，如果是则创建部门
                List<Long> roleList = sysRoleService.selectRoleListByUserId(user.getUserId());
                if (roleList.size() == 0) {
                    return error("获取当前用户失败！");
                }
                Long[] roleIds = new Long[roleList.size()];
                for (int i = 0; i < roleList.size(); i++) {
                    roleIds[i] = roleList.get(i);
                }
                /*
                 * 该系统应该只有4，5，6角色可以新增部门
                 * 2 个人
                 * 4 教育端
                 * 5 企业端(需求方)
                 * 6 企业端(服务商)
                 */
                // 遍历roleList是否有包含4,5,6
                if (roleList.contains(4L) || roleList.contains(5L) || roleList.contains(6L)) {
                    // 更新user对应的部门
                    user.setRoleIds(roleIds);
                    user.setDeptId(deptId);
                    if (StringUtils.isNotNull(nekndCompany.getCompanyLogoUri()))
                        user.setAvatar(nekndCompany.getCompanyLogoUri());
                    sysUserService.updateUser(user);

                    NekndCompany newCompany = nekndCompanyService.getCompanyInfoByCompanyId(deptId.intValue());
                    if (newCompany == null) {
                        return warn("企业信息不存在！");
                    }
                    nekndCompany.setId(newCompany.getId());

                    if (StringUtils.isNotEmpty(nekndCompany.getCompanyName())) {
                        // 企业名称重复校验
                        Boolean existingEnterprise = nekndCompanyService
                                .getExistingEnterprise(nekndCompany.getCompanyName());
                        if (existingEnterprise) {
                            return warn("该名称已存在，请勿重复填写！");
                        }
                    }

                    if (roleList.contains(4L)) {
                        nekndCompany.setStatus("1");// 学校
                    } else {
                        nekndCompany.setStatus("0");// 企业
                    }
                    nekndCompany.setDeptId(deptId);
                    System.out.println(deptId + "认证成功");
                    nekndCompany.setUpdateStatus(1);
                    user.setNickName(nekndCompany.getCompanyName());
                    user.setEmail(nekndCompany.getEmail());
                    if (StringUtils.isNotEmpty(nekndCompany.getCompanyLogoUri())) {
                        user.setAvatar(nekndCompany.getCompanyLogoUri());
                    }
                    sysUserService.updateUser(user);
                    nekndCompanyService.updateNekndCompany(nekndCompany);
                    // 强制退出，清缓存
                    redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY +
                            getLoginUser().getToken());
                } else {
                    return error("当前用户没有权限创建企业！");
                }
                return success("更新成功，请重新登陆");
            }
        }
        LoginUser loginUser = getLoginUser();
        if (loginUser == null)
            return error("获取当前用户失败！");
        SysUser user = loginUser.getUser();
        // 一个账号只能创建一个部门
        SysDept tmpDept = new SysDept();
        tmpDept.setCreateBy(user.getUserName());
        List<SysDept> tmpDeptList = sysDeptService.selectDeptList(tmpDept);
        if (tmpDeptList.size() > 0) {
            return error("一个账号只能创建一个部门！");
        }
        // 校验当前用户是否为企业端或者教育端角色，如果是则创建部门
        List<Long> roleList = sysRoleService.selectRoleListByUserId(user.getUserId());
        if (roleList.size() == 0) {
            return error("获取当前用户失败！");
        }
        Long[] roleIds = new Long[roleList.size()];
        for (int i = 0; i < roleList.size(); i++) {
            roleIds[i] = roleList.get(i);
        }
        /*
         * 该系统应该只有4，5，6角色可以新增部门
         * 2 个人
         * 4 教育端
         * 5 企业端(需求方)
         * 6 企业端(服务商)
         */
        // 遍历roleList是否有包含4,5,6
        if (roleList.contains(4L) || roleList.contains(5L) || roleList.contains(6L)) {
            // 创建部门
            SysDept sysDept = new SysDept();
            sysDept.setParentId(100L);// 固定100,产教融合服务平台
            sysDept.setDeptName(nekndCompany.getCompanyName());// 企业名称
            sysDept.setOrderNum(999);
            sysDept.setLeader("");// 企业联系人
            sysDept.setPhone(nekndCompany.getPhone());
            sysDept.setEmail(nekndCompany.getEmail());
            sysDept.setStatus("0");
            sysDept.setDelFlag("0");
            sysDept.setCreateTime(new Date());
            sysDept.setCreateBy(user.getUserName());
            sysDeptService.insertDept(sysDept);

            // 更新user对应的部门
            user.setRoleIds(roleIds);
            user.setDeptId(sysDept.getDeptId());
            if (StringUtils.isNotNull(nekndCompany.getCompanyLogoUri()))
                user.setAvatar(nekndCompany.getCompanyLogoUri());
            sysUserService.updateUser(user);

            if (StringUtils.isNotEmpty(nekndCompany.getCompanyName())) {
                // 企业名称重复校验
                Boolean existingEnterprise = nekndCompanyService.getExistingEnterprise(nekndCompany.getCompanyName());
                if (existingEnterprise) {
                    return warn("该名称已存在，请勿重复填写！");
                }
            }

            if (roleList.contains(4L)) {
                nekndCompany.setStatus("1");// 学校
            } else {
                nekndCompany.setStatus("0");// 企业
            }
            nekndCompany.setDeptId(sysDept.getDeptId());
            nekndCompany.setUpdateStatus(1);
            System.out.println(sysDept.getDeptId() + "认证成功");
            user.setNickName(nekndCompany.getCompanyName());
            user.setEmail(nekndCompany.getEmail());
            if (StringUtils.isNotEmpty(nekndCompany.getCompanyLogoUri())) {
                user.setAvatar(nekndCompany.getCompanyLogoUri());
            }
            sysUserService.updateUser(user);
            nekndCompanyService.insertNekndCompany(nekndCompany);
            // 强制退出，清缓存
            redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY +
                    getLoginUser().getToken());
        } else {
            return error("当前用户没有权限创建企业！");
        }
        return success("更新成功，请重新登陆");

    }

    /**
     * 修改企业信息
     */
    @PreAuthorize("@ss.hasPermi('system:company:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult edit(@RequestBody NekndCompany nekndCompany) throws RuntimeException {

        Long deptId = nekndCompany.getDeptId();
        if (deptId == null || deptId.equals(107L) || deptId.equals(100L)) {
            return warn("企业信息不存在！");
        }

        if (StringUtils.isNotEmpty(nekndCompany.getCompanyName())) {
            // 企业名称重复校验
            NekndCompany company = nekndCompanyService.getCompanyInfoByCompanyId(deptId.intValue());
            if (StringUtils.isNotEmpty(company.getCompanyName())) {
                if (!nekndCompany.getCompanyName().equals(company.getCompanyName())) {
                    // 企业名称不相等的时候，才会校验需要修改的企业名称和其他企业名称是否重复
                    Boolean existingEnterprise = nekndCompanyService
                            .getExistingEnterprise(nekndCompany.getCompanyName());
                    if (existingEnterprise) {
                        return warn("该名称已存在，请勿重复填写！");
                    }
                }
            }
        }
        // 同时修改部门信息
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(deptId);
        if (StringUtils.isNotEmpty(nekndCompany.getCompanyName())) {
            sysDept.setDeptName(nekndCompany.getCompanyName());
        }
        if (StringUtils.isNotEmpty(nekndCompany.getEmail())) {
            sysDept.setEmail(nekndCompany.getEmail());
        }
        if (StringUtils.isNotEmpty(nekndCompany.getPhone())) {
            sysDept.setPhone(nekndCompany.getPhone());
        }
        try {
            // 同时修改user表信息
            List<SysUser> sysUsers = sysUserService.selectUserByDeptId(deptId);
            if (sysUsers.size() == 1) {
                SysUser sysUser = new SysUser();
                sysUser.setUserId(sysUsers.get(0).getUserId());
                sysUser.setNickName(nekndCompany.getCompanyName());
                sysUser.setEmail(nekndCompany.getEmail());
                if (StringUtils.isNotEmpty(nekndCompany.getCompanyLogoUri())) {
                    sysUser.setAvatar(nekndCompany.getCompanyLogoUri());
                }
                if (StringUtils.isNotEmpty(nekndCompany.getCompanyLogoUri())) {
                    sysUser.setAvatar(nekndCompany.getCompanyLogoUri());
                }
                sysUserService.updateUsers(sysUser);
            }
            sysDeptService.updateDept(sysDept);

            // 同时修改服务需求的服务商名称
            NekndServiceRequirements requirements = new NekndServiceRequirements();
            requirements.setServiceProviderId(deptId.intValue());
            List<NekndServiceRequirements> nekndServiceRequirements = nekndServiceRequirementsService
                    .selectNekndServiceRequirementsList(requirements);
            if (nekndServiceRequirements != null && nekndServiceRequirements.size() > 0) {
                for (NekndServiceRequirements nekndServiceRequirement : nekndServiceRequirements) {
                    NekndServiceRequirements newRequirements = new NekndServiceRequirements();
                    newRequirements.setId(nekndServiceRequirement.getId());
                    newRequirements.setServiceProviderName(nekndCompany.getCompanyName());
                    nekndServiceRequirementsService.updateNekndServiceRequirements(newRequirements);
                }
            }
            // 同时修改项目需求的需求方名称
            NekndSupplyDemand nekndSupplyDemand = new NekndSupplyDemand();
            nekndSupplyDemand.setDeptId(deptId);
            List<NekndSupplyDemand> nekndSupplyDemands = nekndSupplyDemandService.selectDemandList(nekndSupplyDemand);
            if (nekndSupplyDemands != null && nekndSupplyDemands.size() > 0) {
                for (NekndSupplyDemand supplyDemand : nekndSupplyDemands) {
                    NekndSupplyDemand newSupplyDemand = new NekndSupplyDemand();
                    newSupplyDemand.setId(supplyDemand.getId());
                    newSupplyDemand.setDeptName(nekndCompany.getCompanyName());
                    nekndSupplyDemandService.updateNekndSupplyDemand(newSupplyDemand);
                }
            }
            return toAjax(nekndCompanyService.updateNekndCompany(nekndCompany));
        } catch (Exception e) {
            // 记录日志
            e.printStackTrace();
            throw new RuntimeException("修改企业信息失败，请稍后再试。");
        }
    }

    /**
     * 删除企业信息
     */
    @PreAuthorize("@ss.hasPermi('system:company:remove')")
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(nekndCompanyService.deleteNekndCompanyByIds(ids));
    }

    /**
     * 获取所有入驻的企业
     */
    @GetMapping("/getList")
    @Anonymous
    public AjaxResult getList(NekndCompany nekndCompany) {
        List<NekndCompany> list = nekndCompanyService.selectNekndCompanyList(nekndCompany);
        return success(list);
    }

    /**
     * 门户网站-获取名企
     */
    @Anonymous
    @GetMapping("/getEnterprises")
    public TableDataInfo getEnterprises(NekndCompany nekndCompany) {
        startPage();
        List<NekndCompany> list = nekndCompanyService.selectNekndCompanyList(nekndCompany);
        return getDataTable(list);
    }

    /**
     * 按deptid查找公司名称
     */
    @Anonymous
    @GetMapping("/selectDeptId/{deptId}")
    public NekndCompany selectName(@PathVariable Integer deptId) {
        NekndCompany nekndCompany = nekndCompanyService.selectName(deptId);
        return nekndCompany;
    }

    /**
     * 扫码注册企业
     */
    @Anonymous
    @PostMapping("/registerCompany")
    public AjaxResult registerCompany(@RequestBody NekndCompany nekndCompany) {
        if (StringUtils.isEmpty(nekndCompany.getPhone())) {
            return error("手机号不能为空");
        }
        if (StringUtils.isEmpty(nekndCompany.getCompanyName())) {
            return error("企业名称不能为空");
        }
        if (StringUtils.isEmpty(nekndCompany.getBusinessLicenseNo())) {
            return error("营业执照号不能为空");
        }
        if (StringUtils.isEmpty(nekndCompany.getSummary())) {
            return error("企业简介不能为空");
        }

        SysUser tmpUser = new SysUser();
        tmpUser.setUserName(nekndCompany.getPhone());
        if (!sysUserService.checkUserNameUnique(tmpUser)) {
            return error("注册'" + nekndCompany.getPhone() + "'失败，注册手机号已存在");
        }

        // 创建部门
        SysDept sysDept = new SysDept();
        sysDept.setParentId(100L);// 固定100,产教融合服务平台
        sysDept.setDeptName(nekndCompany.getCompanyName());// 企业名称
        sysDept.setOrderNum(999);
        sysDept.setLeader(nekndCompany.getCompanyName());// 企业联系人
        sysDept.setPhone(nekndCompany.getPhone());
        sysDept.setEmail(nekndCompany.getEmail());
        sysDept.setStatus("0");
        sysDept.setDelFlag("0");
        sysDept.setCreateTime(new Date());
        sysDept.setCreateBy("扫码注册");
        sysDeptService.insertDept(sysDept);

        // 新增企业用户信息
        SysUser sysUser = new SysUser();
        sysUser.setDeptId(sysDept.getDeptId());// 取上面的deptId
        sysUser.setUserName(nekndCompany.getPhone());// 手机号
        sysUser.setNickName(nekndCompany.getCompanyName());
        sysUser.setEmail(nekndCompany.getEmail());
        sysUser.setPhonenumber(nekndCompany.getPhone());
        sysUser.setSex(Constants.ZERO_STR);
        sysUser.setAvatar("");
        sysUser.setStatus(Constants.ZERO_STR);
        sysUser.setDelFlag(Constants.ZERO_STR);
        sysUser.setPassword(SecurityUtils.encryptPassword("123456"));
        sysUser.setRemark("企业扫码注册");
        boolean regFlag = sysUserService.registerUser(sysUser);
        /* 设置角色 */
        String registerBody = "5,6";
        String[] roleIdArr = registerBody.split(",");
        Long[] roleIds = new Long[roleIdArr.length];// 将string[]roleIdArr转为Long[]
        for (int i = 0; i < roleIdArr.length; i++) {
            roleIds[i] = Long.parseLong(roleIdArr[i]);
        }
        sysUser.setRoleIds(roleIds);
        sysUserService.insertUserAuth(sysUser.getUserId(), sysUser.getRoleIds());
        if (!regFlag) {
            return error("注册失败,请联系系统管理人员");
        } else {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(nekndCompany.getCompanyName(),
                    Constants.REGISTER, MessageUtils.message("user.register.success")));
        }

        // 新增企业信息表
        // 手机号(必填) phone
        // 企业名称(必填) company_name
        // 营业执照号(必填) business_license_no
        // 企业简介（必填） summary
        // 地址(选填) address
        // 邮箱(选填) email
        // 所属行业(选填) industry
        // 企业图片（选填） introduce_picture_uri
        // 企业视频（选填） introduce_video_uri
        nekndCompany.setDeptId(sysDept.getDeptId());
        nekndCompany.setStatus(Constants.ZERO_STR);// 0企业
        nekndCompany.setUpdateStatus(Constants.ZERO_INT);// 更新详情状态（0:未更新，1：已更新）
        nekndCompanyService.insertNekndCompany(nekndCompany);
        return success("注册企业成功!");
    }

    /**
     * 获取自己的企业信息
     */
    @GetMapping("/getCompany/own")
    public AjaxResult getCompany() {
        LoginUser loginUser = getLoginUser();
        if (loginUser == null)
            return error("获取当前用户失败！");
        SysUser user = loginUser.getUser();
        if (user.getDeptId() == null) {
            return warn("请先填写公司信息！");
        }
        return success(nekndCompanyService.getCompanyInfoByCompanyId(Integer.parseInt(user.getDeptId().toString())));
    }

    /**
     * 门户查询企业信息列表
     * 入参：
     * address 地址
     * companyName 公司名称
     * industry 所属行业
     */
    @Anonymous
    @GetMapping("/getCompanyList")
    public TableDataInfo getCompanyList(NekndCompany nekndCompany) {
        String status = nekndCompany.getStatus();
        if (StringUtils.isEmpty(status)) {
            nekndCompany.setStatus("");// 默认查询企业和学校
        }

        CustomPageUtils.pageStart();
        List<NekndCompany> list = nekndCompanyService.selectNekndCompanyList(nekndCompany);

        // int count = nekndCompanyService.getCountCompany(status);
        List<NekndCompany> newList = new ArrayList<>();
        for (NekndCompany company : list) {
            Long deptId = company.getDeptId();
            NekndEmploy nekndEmploy = new NekndEmploy();
            nekndEmploy.setDeptId(deptId);
            nekndEmploy.setReviewStatus("1");
            nekndEmploy.setStatus("1");
            List<NekndEmploy> nekndEmploys = nekndEmployService.selectNekndEmployList(nekndEmploy);
            if (nekndEmploys == null || nekndEmploys.size() <= 0) {
                company.setNekndEmploy(null);
            } else {
                NekndEmploy firstEmploy = nekndEmploys.get(0);
                company.setNekndEmploy(firstEmploy);
            }
            newList.add(company);
        }
        return CustomPageUtils.pageEnd(list, newList);

        // String status = nekndCompany.getStatus();
        // if(StringUtils.isEmpty(status)){
        // nekndCompany.setStatus("");//默认查询企业和学校
        // }
        //
        // /** body格式获取分页*/
        // PageDomain pageDomain = TableSupport.buildPageRequest();
        // Integer pageNum = pageDomain.getPageNum();
        // Integer pageSize = pageDomain.getPageSize();
        // if(pageNum==null || pageSize==null){
        // throw new RuntimeException("未获取到分页参数值");
        // }
        // // 开启分页
        // PageHelper.startPage(pageNum,pageSize);
        // List<NekndCompany> list =
        nekndCompanyService.selectNekndCompanyList(nekndCompany);
        // // 这一步的作用主要是为了获取分页信息
        // PageInfo pageInfo = new PageInfo<>(list);
        //
        //
        // List<NekndCompany> newList = new ArrayList<>();
        // for (NekndCompany company : list) {
        // Long deptId = company.getDeptId();
        // NekndEmploy nekndEmploy = new NekndEmploy();
        // nekndEmploy.setDeptId(deptId);
        // List<NekndEmploy> nekndEmploys =
        nekndEmployService.selectNekndEmployList(nekndEmploy);
        // if(nekndEmploys == null || nekndEmploys.size() <= 0){
        // company.setNekndEmploy(null);
        // }else {
        // NekndEmploy firstEmploy = nekndEmploys.get(0);
        // company.setNekndEmploy(firstEmploy);
        // }
        // newList.add(company);
        // }
        // pageInfo.setList(newList);
        // TableDataInfo rspData = new TableDataInfo();
        // rspData.setCode(HttpStatus.SUCCESS);
        // rspData.setMsg("查询成功");
        // rspData.setRows(pageInfo.getList());
        // rspData.setTotal(pageInfo.getTotal());
        // return rspData;
    }

    /**
     * 门户查询企业信息详情
     * 入参：
     * id 企业id
     */
    @Anonymous
    @GetMapping("/getCompanyInfo/{id}")
    public AjaxResult getCompanyInfo(@PathVariable Integer id) {
        NekndCompany nekndCompany = nekndCompanyService.selectNekndCompanyById(id);
        if (nekndCompany == null)
            return error("查询企业信息失败！");
        return success(nekndCompany);
    }

    @GetMapping("/getUserIdAndName/{deptId}")
    public AjaxResult getUserIdAndName(@PathVariable Long deptId) {
        NekndCompany company = nekndCompanyService.getCompanyInfoByCompanyId(deptId.intValue());
        List<SysUser> user = sysUserService.selectUserByDeptId(deptId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("userId", user.get(0).getUserId());
        map.put("name", company.getCompanyName());
        return success(map);
    }

    /**
     * 入驻企业数量
     */
    @Anonymous
    @GetMapping("/getCompanyCount")
    public AjaxResult getCompanyCount() {
        return success(nekndCompanyService.getCountCompanyNew());
    }

    /**
     * 入驻教育机构数量
     */
    @Anonymous
    @GetMapping("/getCountEducation")
    public AjaxResult getCountEducation() {
        return success(nekndCompanyService.getCountEducation());
    }

    /**
     * 企业类别分布
     */
    @Anonymous
    @GetMapping("getBusinessCategories")
    public AjaxResult getBusinessCategories() {
        List<HashMap<String, Object>> oldBusinessCategories = nekndCompanyService.getBusinessCategories();
        Map<String, List<Object>> businessCategories = MapUtils.convertToPopularMap(oldBusinessCategories);
        return success(businessCategories);
    }

    /**
     * 监测平台的企业类别数据（园区）
     */
    @Anonymous
    @GetMapping("monitoring/companyType")
    public TableDataInfo getCompanyType(@RequestParam String status) {
        // 分组查询
        List<HashMap<String, Object>> companyList = nekndCompanyService.getCompanyType(status);
        return getDataTable(companyList);
    }
}

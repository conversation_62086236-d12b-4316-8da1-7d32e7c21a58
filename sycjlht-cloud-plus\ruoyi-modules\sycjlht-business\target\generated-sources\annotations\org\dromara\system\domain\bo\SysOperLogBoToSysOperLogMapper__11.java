package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__489;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__11;
import org.dromara.system.domain.SysOperLog;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__489.class,
    uses = {SysOperLogBoToOperLogEventMapper__11.class,OperLogEventToSysOperLogBoMapper__11.class},
    imports = {}
)
public interface SysOperLogBoToSysOperLogMapper__11 extends BaseMapper<SysOperLogBo, SysOperLog> {
}

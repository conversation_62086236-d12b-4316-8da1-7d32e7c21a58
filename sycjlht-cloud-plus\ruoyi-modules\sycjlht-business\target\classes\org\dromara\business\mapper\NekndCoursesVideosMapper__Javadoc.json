{"doc": "\n 课程视频Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-08\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCoursesVideosByVideoId", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询课程视频\r\n\r\n @param videoId 课程视频主键\r\n @return 课程视频\r\n"}, {"name": "selectNekndCoursesVideosList", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": "\n 查询课程视频列表\r\n\r\n @param nekndCoursesVideos 课程视频\r\n @return 课程视频集合\r\n"}, {"name": "insertNekndCoursesVideos", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": "\n 新增课程视频\r\n\r\n @param nekndCoursesVideos 课程视频\r\n @return 结果\r\n"}, {"name": "updateNekndCoursesVideos", "paramTypes": ["org.dromara.business.domain.NekndCoursesVideos"], "doc": "\n 修改课程视频\r\n\r\n @param nekndCoursesVideos 课程视频\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesVideosByVideoId", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除课程视频\r\n\r\n @param videoId 课程视频主键\r\n @return 结果\r\n"}, {"name": "deleteNekndCoursesVideosByVideoIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除课程视频\r\n\r\n @param videoIds 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
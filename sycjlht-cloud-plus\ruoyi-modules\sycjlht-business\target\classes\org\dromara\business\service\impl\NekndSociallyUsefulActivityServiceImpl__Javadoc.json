{"doc": "\n 公益活动Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-11-05\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSociallyUsefulActivityById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询公益活动\r\n\r\n @param id 公益活动主键\r\n @return 公益活动\r\n"}, {"name": "selectNekndSociallyUsefulActivityList", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": "\n 查询公益活动列表\r\n\r\n @param nekndSociallyUsefulActivity 公益活动\r\n @return 公益活动\r\n"}, {"name": "insertNekndSociallyUsefulActivity", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": "\n 新增公益活动\r\n\r\n @param nekndSociallyUsefulActivity 公益活动\r\n @return 结果\r\n"}, {"name": "updateNekndSociallyUsefulActivity", "paramTypes": ["org.dromara.business.domain.NekndSociallyUsefulActivity"], "doc": "\n 修改公益活动\r\n\r\n @param nekndSociallyUsefulActivity 公益活动\r\n @return 结果\r\n"}, {"name": "deleteNekndSociallyUsefulActivityByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除公益活动\r\n\r\n @param ids 需要删除的公益活动主键\r\n @return 结果\r\n"}, {"name": "deleteNekndSociallyUsefulActivityById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除公益活动信息\r\n\r\n @param id 公益活动主键\r\n @return 结果\r\n"}], "constructors": []}
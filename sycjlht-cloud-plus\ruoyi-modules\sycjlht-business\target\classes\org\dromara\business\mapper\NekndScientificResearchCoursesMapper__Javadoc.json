{"doc": "\n 科普研学课程Mapper接口\r\n`\r\n <AUTHOR>\r\n @date 2025-06-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchCoursesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询科普研学课程\r\n\r\n @param id 科普研学课程主键\r\n @return 科普研学课程\r\n"}, {"name": "selectNekndScientificResearchCoursesList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchCourses"], "doc": "\n 查询科普研学课程列表\r\n\r\n @param nekndScientificResearchCourses 科普研学课程\r\n @return 科普研学课程集合\r\n"}, {"name": "insertNekndScientificResearchCourses", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchCourses"], "doc": "\n 新增科普研学课程\r\n\r\n @param nekndScientificResearchCourses 科普研学课程\r\n @return 结果\r\n"}, {"name": "updateNekndScientificResearchCourses", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchCourses"], "doc": "\n 修改科普研学课程\r\n\r\n @param nekndScientificResearchCourses 科普研学课程\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchCoursesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除科普研学课程\r\n\r\n @param id 科普研学课程主键\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchCoursesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除科普研学课程\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
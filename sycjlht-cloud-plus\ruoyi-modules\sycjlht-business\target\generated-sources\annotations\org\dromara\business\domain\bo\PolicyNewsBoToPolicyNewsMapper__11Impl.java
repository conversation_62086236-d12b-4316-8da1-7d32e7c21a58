package org.dromara.business.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.business.domain.PolicyNews;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T10:47:16+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class PolicyNewsBoToPolicyNewsMapper__11Impl implements PolicyNewsBoToPolicyNewsMapper__11 {

    @Override
    public PolicyNews convert(PolicyNewsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PolicyNews policyNews = new PolicyNews();

        policyNews.setCreateBy( arg0.getCreateBy() );
        policyNews.setCreateDept( arg0.getCreateDept() );
        policyNews.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            policyNews.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        policyNews.setSearchValue( arg0.getSearchValue() );
        policyNews.setUpdateBy( arg0.getUpdateBy() );
        policyNews.setUpdateTime( arg0.getUpdateTime() );
        policyNews.setBelongDistrict( arg0.getBelongDistrict() );
        policyNews.setBelongPark( arg0.getBelongPark() );
        policyNews.setCoverUri( arg0.getCoverUri() );
        policyNews.setFileType( arg0.getFileType() );
        policyNews.setFileTypeFunds( arg0.getFileTypeFunds() );
        policyNews.setId( arg0.getId() );
        policyNews.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        policyNews.setNewsContent( arg0.getNewsContent() );
        policyNews.setNewsPosition( arg0.getNewsPosition() );
        policyNews.setNewsTitle( arg0.getNewsTitle() );
        policyNews.setNewsType( arg0.getNewsType() );
        policyNews.setPlanType( arg0.getPlanType() );
        policyNews.setPolicy2category( arg0.getPolicy2category() );
        policyNews.setPolicyCategory( arg0.getPolicyCategory() );
        policyNews.setSourceTitle( arg0.getSourceTitle() );
        policyNews.setStatus( arg0.getStatus() );

        return policyNews;
    }

    @Override
    public PolicyNews convert(PolicyNewsBo arg0, PolicyNews arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setBelongDistrict( arg0.getBelongDistrict() );
        arg1.setBelongPark( arg0.getBelongPark() );
        arg1.setCoverUri( arg0.getCoverUri() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setFileTypeFunds( arg0.getFileTypeFunds() );
        arg1.setId( arg0.getId() );
        arg1.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        arg1.setNewsContent( arg0.getNewsContent() );
        arg1.setNewsPosition( arg0.getNewsPosition() );
        arg1.setNewsTitle( arg0.getNewsTitle() );
        arg1.setNewsType( arg0.getNewsType() );
        arg1.setPlanType( arg0.getPlanType() );
        arg1.setPolicy2category( arg0.getPolicy2category() );
        arg1.setPolicyCategory( arg0.getPolicyCategory() );
        arg1.setSourceTitle( arg0.getSourceTitle() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}

{"doc": "\n 报告权限管理Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-09-20\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询报告权限管理\r\n\r\n @param id 报告权限管理主键\r\n @return 报告权限管理\r\n"}, {"name": "selectNekndReportPrivilegeList", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 查询报告权限管理列表\r\n\r\n @param nekndReportPrivilege 报告权限管理\r\n @return 报告权限管理集合\r\n"}, {"name": "insertNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 新增报告权限管理\r\n\r\n @param nekndReportPrivilege 报告权限管理\r\n @return 结果\r\n"}, {"name": "updateNekndReportPrivilege", "paramTypes": ["org.dromara.business.domain.NekndReportPrivilege"], "doc": "\n 修改报告权限管理\r\n\r\n @param nekndReportPrivilege 报告权限管理\r\n @return 结果\r\n"}, {"name": "deleteNekndReportPrivilegeByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除报告权限管理\r\n\r\n @param ids 需要删除的报告权限管理主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndReportPrivilegeById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除报告权限管理信息\r\n\r\n @param id 报告权限管理主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "\n 咸宁对接的数据的同步日志Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2025-07-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndDataSyncRecordById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询咸宁对接的数据的同步日志\r\n\r\n @param id 咸宁对接的数据的同步日志主键\r\n @return 咸宁对接的数据的同步日志\r\n"}, {"name": "selectNekndDataSyncRecordList", "paramTypes": ["org.dromara.business.domain.NekndDataSyncRecord"], "doc": "\n 查询咸宁对接的数据的同步日志列表\r\n\r\n @param nekndDataSyncRecord 咸宁对接的数据的同步日志\r\n @return 咸宁对接的数据的同步日志\r\n"}, {"name": "insertNekndDataSyncRecord", "paramTypes": ["org.dromara.business.domain.NekndDataSyncRecord"], "doc": "\n 新增咸宁对接的数据的同步日志\r\n\r\n @param nekndDataSyncRecord 咸宁对接的数据的同步日志\r\n @return 结果\r\n"}, {"name": "updateNekndDataSyncRecord", "paramTypes": ["org.dromara.business.domain.NekndDataSyncRecord"], "doc": "\n 修改咸宁对接的数据的同步日志\r\n\r\n @param nekndDataSyncRecord 咸宁对接的数据的同步日志\r\n @return 结果\r\n"}, {"name": "deleteNekndDataSyncRecordByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除咸宁对接的数据的同步日志\r\n\r\n @param ids 需要删除的咸宁对接的数据的同步日志主键\r\n @return 结果\r\n"}, {"name": "deleteNekndDataSyncRecordById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除咸宁对接的数据的同步日志信息\r\n\r\n @param id 咸宁对接的数据的同步日志主键\r\n @return 结果\r\n"}], "constructors": []}
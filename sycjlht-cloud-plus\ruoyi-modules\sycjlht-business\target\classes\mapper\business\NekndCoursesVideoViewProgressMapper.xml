<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndCoursesVideoViewProgressMapper">
    
    <resultMap type="NekndCoursesVideoViewProgress" id="NekndCoursesVideoViewProgressResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="videoId"    column="video_id"    />
        <result property="viewingProgressTime"    column="viewing_progress_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNekndCoursesVideoViewProgressVo">
        select id, user_id, video_id, viewing_progress_time, create_time, update_time from neknd_courses_video_view_progress
    </sql>

    <select id="selectNekndCoursesVideoViewProgressList" parameterType="NekndCoursesVideoViewProgress" resultMap="NekndCoursesVideoViewProgressResult">
        <include refid="selectNekndCoursesVideoViewProgressVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectNekndCoursesVideoViewProgressById" parameterType="Integer" resultMap="NekndCoursesVideoViewProgressResult">
        <include refid="selectNekndCoursesVideoViewProgressVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndCoursesVideoViewProgress" parameterType="NekndCoursesVideoViewProgress" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_courses_video_view_progress
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="videoId != null">video_id,</if>
            <if test="viewingProgressTime != null">viewing_progress_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="videoId != null">#{videoId},</if>
            <if test="viewingProgressTime != null">#{viewingProgressTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNekndCoursesVideoViewProgress" parameterType="NekndCoursesVideoViewProgress">
        update neknd_courses_video_view_progress
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="viewingProgressTime != null">viewing_progress_time = #{viewingProgressTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndCoursesVideoViewProgressById" parameterType="Integer">
        delete from neknd_courses_video_view_progress where id = #{id}
    </delete>

    <delete id="deleteNekndCoursesVideoViewProgressByIds" parameterType="String">
        delete from neknd_courses_video_view_progress where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
{"doc": "\n 科普研学导师Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2025-06-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchMentorById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询科普研学导师\r\n\r\n @param id 科普研学导师主键\r\n @return 科普研学导师\r\n"}, {"name": "selectNekndScientificResearchMentorList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": "\n 查询科普研学导师列表\r\n\r\n @param nekndScientificResearchMentor 科普研学导师\r\n @return 科普研学导师\r\n"}, {"name": "insertNekndScientificResearchMentor", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": "\n 新增科普研学导师\r\n\r\n @param nekndScientificResearchMentor 科普研学导师\r\n @return 结果\r\n"}, {"name": "updateNekndScientificResearchMentor", "paramTypes": ["org.dromara.business.domain.NekndScientificResearchMentor"], "doc": "\n 修改科普研学导师\r\n\r\n @param nekndScientificResearchMentor 科普研学导师\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchMentorByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除科普研学导师\r\n\r\n @param ids 需要删除的科普研学导师主键\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchMentorById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除科普研学导师信息\r\n\r\n @param id 科普研学导师主键\r\n @return 结果\r\n"}, {"name": "selectResearchMentorByIdAndResearchId", "paramTypes": ["int", "int"], "doc": "\n 查询指定导师是否已经添加过指定科研项目\r\n @param mentorId\r\n @param researchId\r\n @return true  表示已经添加过 false 表示没有添加过\r\n"}], "constructors": []}
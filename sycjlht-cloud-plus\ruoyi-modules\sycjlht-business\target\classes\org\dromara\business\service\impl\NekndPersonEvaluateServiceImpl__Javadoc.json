{"doc": "\n 企业/学校评价Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-11\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndPersonEvaluateById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询企业/学校评价\r\n\r\n @param id 企业/学校评价主键\r\n @return 企业/学校评价\r\n"}, {"name": "selectNekndPersonEvaluateList", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": "\n 查询企业/学校评价列表\r\n\r\n @param nekndPersonEvaluate 企业/学校评价\r\n @return 企业/学校评价\r\n"}, {"name": "insertNekndPersonEvaluate", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": "\n 新增企业/学校评价\r\n\r\n @param nekndPersonEvaluate 企业/学校评价\r\n @return 结果\r\n"}, {"name": "updateNekndPersonEvaluate", "paramTypes": ["org.dromara.business.domain.NekndPersonEvaluate"], "doc": "\n 修改企业/学校评价\r\n\r\n @param nekndPersonEvaluate 企业/学校评价\r\n @return 结果\r\n"}, {"name": "deleteNekndPersonEvaluateByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除企业/学校评价\r\n\r\n @param ids 需要删除的企业/学校评价主键\r\n @return 结果\r\n"}, {"name": "deleteNekndPersonEvaluateById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除企业/学校评价信息\r\n\r\n @param id 企业/学校评价主键\r\n @return 结果\r\n"}], "constructors": []}
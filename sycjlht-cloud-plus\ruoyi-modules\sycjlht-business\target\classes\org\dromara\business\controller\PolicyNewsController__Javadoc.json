{"doc": " 政策新闻信息\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询政策新闻信息列表\n"}, {"name": "export", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出政策新闻信息列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取政策新闻信息详细信息\n"}, {"name": "add", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 新增政策新闻信息\n"}, {"name": "edit", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo"], "doc": " 修改政策新闻信息\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除政策新闻信息\n"}, {"name": "publicList", "paramTypes": ["org.dromara.business.domain.bo.PolicyNewsBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 公开接口：查询政策新闻信息列表（无需权限）\n"}, {"name": "publicGetInfo", "paramTypes": ["java.lang.Long"], "doc": " 公开接口：获取政策新闻信息详细信息（无需权限）\n"}], "constructors": []}
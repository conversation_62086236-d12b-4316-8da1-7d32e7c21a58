package org.dromara.business.domain;

import javax.annotation.processing.Generated;
import org.dromara.business.domain.vo.PolicyNewsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T10:47:16+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class PolicyNewsToPolicyNewsVoMapper__11Impl implements PolicyNewsToPolicyNewsVoMapper__11 {

    @Override
    public PolicyNewsVo convert(PolicyNews arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PolicyNewsVo policyNewsVo = new PolicyNewsVo();

        policyNewsVo.setBelongDistrict( arg0.getBelongDistrict() );
        policyNewsVo.setBelongPark( arg0.getBelongPark() );
        policyNewsVo.setCoverUri( arg0.getCoverUri() );
        policyNewsVo.setCreateTime( arg0.getCreateTime() );
        policyNewsVo.setFileType( arg0.getFileType() );
        policyNewsVo.setFileTypeFunds( arg0.getFileTypeFunds() );
        policyNewsVo.setId( arg0.getId() );
        policyNewsVo.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        policyNewsVo.setNewsContent( arg0.getNewsContent() );
        policyNewsVo.setNewsPosition( arg0.getNewsPosition() );
        policyNewsVo.setNewsTitle( arg0.getNewsTitle() );
        policyNewsVo.setNewsType( arg0.getNewsType() );
        policyNewsVo.setNextId( arg0.getNextId() );
        policyNewsVo.setNextTitle( arg0.getNextTitle() );
        policyNewsVo.setPlanType( arg0.getPlanType() );
        policyNewsVo.setPolicy2category( arg0.getPolicy2category() );
        policyNewsVo.setPolicyCategory( arg0.getPolicyCategory() );
        policyNewsVo.setPreviousId( arg0.getPreviousId() );
        policyNewsVo.setPreviousTitle( arg0.getPreviousTitle() );
        policyNewsVo.setSourceTitle( arg0.getSourceTitle() );
        policyNewsVo.setStatus( arg0.getStatus() );
        policyNewsVo.setUpdateTime( arg0.getUpdateTime() );

        return policyNewsVo;
    }

    @Override
    public PolicyNewsVo convert(PolicyNews arg0, PolicyNewsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBelongDistrict( arg0.getBelongDistrict() );
        arg1.setBelongPark( arg0.getBelongPark() );
        arg1.setCoverUri( arg0.getCoverUri() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setFileType( arg0.getFileType() );
        arg1.setFileTypeFunds( arg0.getFileTypeFunds() );
        arg1.setId( arg0.getId() );
        arg1.setIsThematicMeeting( arg0.getIsThematicMeeting() );
        arg1.setNewsContent( arg0.getNewsContent() );
        arg1.setNewsPosition( arg0.getNewsPosition() );
        arg1.setNewsTitle( arg0.getNewsTitle() );
        arg1.setNewsType( arg0.getNewsType() );
        arg1.setNextId( arg0.getNextId() );
        arg1.setNextTitle( arg0.getNextTitle() );
        arg1.setPlanType( arg0.getPlanType() );
        arg1.setPolicy2category( arg0.getPolicy2category() );
        arg1.setPolicyCategory( arg0.getPolicyCategory() );
        arg1.setPreviousId( arg0.getPreviousId() );
        arg1.setPreviousTitle( arg0.getPreviousTitle() );
        arg1.setSourceTitle( arg0.getSourceTitle() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}

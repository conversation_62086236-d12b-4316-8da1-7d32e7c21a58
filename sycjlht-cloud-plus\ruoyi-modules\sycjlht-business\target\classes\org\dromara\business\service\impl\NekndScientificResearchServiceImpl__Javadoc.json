{"doc": "\n 科普研学信息Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-06-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndScientificResearchById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询科普研学信息\r\n\r\n @param id 科普研学信息主键\r\n @return 科普研学信息\r\n"}, {"name": "selectNekndScientificResearchList", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": "\n 查询科普研学信息列表\r\n\r\n @param nekndScientificResearch 科普研学信息\r\n @return 科普研学信息\r\n"}, {"name": "insertNekndScientificResearch", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": "\n 新增科普研学信息\r\n\r\n @param nekndScientificResearch 科普研学信息\r\n @return 结果\r\n"}, {"name": "updateNekndScientificResearch", "paramTypes": ["org.dromara.business.domain.NekndScientificResearch"], "doc": "\n 修改科普研学信息\r\n\r\n @param nekndScientificResearch 科普研学信息\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除科普研学信息\r\n\r\n @param ids 需要删除的科普研学信息主键\r\n @return 结果\r\n"}, {"name": "deleteNekndScientificResearchById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除科普研学信息信息\r\n\r\n @param id 科普研学信息主键\r\n @return 结果\r\n"}], "constructors": []}
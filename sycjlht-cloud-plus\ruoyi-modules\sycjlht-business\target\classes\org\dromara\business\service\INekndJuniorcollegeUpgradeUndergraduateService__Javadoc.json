{"doc": "\n 继续教育信息Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-10-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndJuniorcollegeUpgradeUndergraduateById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询继续教育信息\r\n\r\n @param id 继续教育信息主键\r\n @return 继续教育信息\r\n"}, {"name": "selectNekndJuniorcollegeUpgradeUndergraduateList", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 查询继续教育信息列表\r\n\r\n @param nekndJuniorcollegeUpgradeUndergraduate 继续教育信息\r\n @return 继续教育信息集合\r\n"}, {"name": "insertNekndJuniorcollegeUpgradeUndergraduate", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 新增继续教育信息\r\n\r\n @param nekndJuniorcollegeUpgradeUndergraduate 继续教育信息\r\n @return 结果\r\n"}, {"name": "updateNekndJuniorcollegeUpgradeUndergraduate", "paramTypes": ["org.dromara.business.domain.NekndJuniorcollegeUpgradeUndergraduate"], "doc": "\n 修改继续教育信息\r\n\r\n @param nekndJuniorcollegeUpgradeUndergraduate 继续教育信息\r\n @return 结果\r\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除继续教育信息\r\n\r\n @param ids 需要删除的继续教育信息主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndJuniorcollegeUpgradeUndergraduateById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除继续教育信息信息\r\n\r\n @param id 继续教育信息主键\r\n @return 结果\r\n"}], "constructors": []}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndChartsMapper">
    
    <resultMap type="NekndCharts" id="NekndChartsResult">
        <result property="id"    column="id"    />
        <result property="fileTitle"    column="file_title"    />
        <result property="fileUri"    column="file_uri"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectNekndChartsVo">
        select id, file_title, file_uri, del_flag, create_by, create_time, update_by, update_time from neknd_charts
    </sql>

    <select id="selectNekndChartsList" parameterType="NekndCharts" resultMap="NekndChartsResult">
        <include refid="selectNekndChartsVo"/>
        <where>
            del_flag = 0
            <if test="fileTitle != null  and fileTitle != ''"> and file_title like concat('%', #{fileTitle}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndChartsById" parameterType="Integer" resultMap="NekndChartsResult">
        <include refid="selectNekndChartsVo"/>
        where del_flag=0 and id = #{id}
    </select>
        
    <insert id="insertNekndCharts" parameterType="NekndCharts" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_charts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileTitle != null">file_title,</if>
            <if test="fileUri != null">file_uri,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileTitle != null">#{fileTitle},</if>
            <if test="fileUri != null">#{fileUri},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateNekndCharts" parameterType="NekndCharts">
        update neknd_charts
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileTitle != null">file_title = #{fileTitle},</if>
            <if test="fileUri != null">file_uri = #{fileUri},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteNekndChartsById" parameterType="Integer">
        update neknd_charts set del_flag=2 where id = #{id}
    </update>

    <update id="deleteNekndChartsByIds" parameterType="String">
        update neknd_charts set del_flag=2 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
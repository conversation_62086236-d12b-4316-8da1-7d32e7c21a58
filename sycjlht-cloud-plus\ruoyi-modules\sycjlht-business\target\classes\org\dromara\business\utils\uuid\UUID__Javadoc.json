{"doc": "\n 提供通用唯一识别码（universally unique identifier）（UUID）实现\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "mostSigBits", "doc": "此UUID的最高64有效位 "}, {"name": "leastSigBits", "doc": "此UUID的最低64有效位 "}], "enumConstants": [], "methods": [{"name": "fastUUID", "paramTypes": [], "doc": "\n 获取类型 4（伪随机生成的）UUID 的静态工厂。\r\n\r\n @return 随机生成的 {@code UUID}\r\n"}, {"name": "randomUUID", "paramTypes": [], "doc": "\n 获取类型 4（伪随机生成的）UUID 的静态工厂。 使用加密的强伪随机数生成器生成该 UUID。\r\n\r\n @return 随机生成的 {@code UUID}\r\n"}, {"name": "randomUUID", "paramTypes": ["boolean"], "doc": "\n 获取类型 4（伪随机生成的）UUID 的静态工厂。 使用加密的强伪随机数生成器生成该 UUID。\r\n\r\n @param isSecure 是否使用{@link SecureRandom}如果是可以获得更安全的随机码，否则可以得到更好的性能\r\n @return 随机生成的 {@code UUID}\r\n"}, {"name": "nameUUIDFromBytes", "paramTypes": ["byte[]"], "doc": "\n 根据指定的字节数组获取类型 3（基于名称的）UUID 的静态工厂。\r\n\r\n @param name 用于构造 UUID 的字节数组。\r\n\r\n @return 根据指定数组生成的 {@code UUID}\r\n"}, {"name": "fromString", "paramTypes": ["java.lang.String"], "doc": "\n 根据 {@link #toString()} 方法中描述的字符串标准表示形式创建{@code UUID}。\r\n\r\n @param name 指定 {@code UUID} 字符串\r\n @return 具有指定值的 {@code UUID}\r\n @throws IllegalArgumentException 如果 name 与 {@link #toString} 中描述的字符串表示形式不符抛出此异常\r\n\r\n"}, {"name": "getLeastSignificantBits", "paramTypes": [], "doc": "\n 返回此 UUID 的 128 位值中的最低有效 64 位。\r\n\r\n @return 此 UUID 的 128 位值中的最低有效 64 位。\r\n"}, {"name": "getMostSignificantBits", "paramTypes": [], "doc": "\n 返回此 UUID 的 128 位值中的最高有效 64 位。\r\n\r\n @return 此 UUID 的 128 位值中最高有效 64 位。\r\n"}, {"name": "version", "paramTypes": [], "doc": "\n 与此 {@code UUID} 相关联的版本号. 版本号描述此 {@code UUID} 是如何生成的。\r\n <p>\r\n 版本号具有以下含意:\r\n <ul>\r\n <li>1 基于时间的 UUID\r\n <li>2 DCE 安全 UUID\r\n <li>3 基于名称的 UUID\r\n <li>4 随机生成的 UUID\r\n </ul>\r\n\r\n @return 此 {@code UUID} 的版本号\r\n"}, {"name": "variant", "paramTypes": [], "doc": "\n 与此 {@code UUID} 相关联的变体号。变体号描述 {@code UUID} 的布局。\r\n <p>\r\n 变体号具有以下含意：\r\n <ul>\r\n <li>0 为 NCS 向后兼容保留\r\n <li>2 <a href=\"http://www.ietf.org/rfc/rfc4122.txt\">IETF&nbsp;RFC&nbsp;4122</a>(Leach-Salz), 用于此类\r\n <li>6 保留，微软向后兼容\r\n <li>7 保留供以后定义使用\r\n </ul>\r\n\r\n @return 此 {@code UUID} 相关联的变体号\r\n"}, {"name": "timestamp", "paramTypes": [], "doc": "\n 与此 UUID 相关联的时间戳值。\r\n\r\n <p>\r\n 60 位的时间戳值根据此 {@code UUID} 的 time_low、time_mid 和 time_hi 字段构造。<br>\r\n 所得到的时间戳以 100 毫微秒为单位，从 UTC（通用协调时间） 1582 年 10 月 15 日零时开始。\r\n\r\n <p>\r\n 时间戳值仅在在基于时间的 UUID（其 version 类型为 1）中才有意义。<br>\r\n 如果此 {@code UUID} 不是基于时间的 UUID，则此方法抛出 UnsupportedOperationException。\r\n\r\n @throws UnsupportedOperationException 如果此 {@code UUID} 不是 version 为 1 的 UUID。\r\n"}, {"name": "clockSequence", "paramTypes": [], "doc": "\n 与此 UUID 相关联的时钟序列值。\r\n\r\n <p>\r\n 14 位的时钟序列值根据此 UUID 的 clock_seq 字段构造。clock_seq 字段用于保证在基于时间的 UUID 中的时间唯一性。\r\n <p>\r\n {@code clockSequence} 值仅在基于时间的 UUID（其 version 类型为 1）中才有意义。 如果此 UUID 不是基于时间的 UUID，则此方法抛出\r\n UnsupportedOperationException。\r\n\r\n @return 此 {@code UUID} 的时钟序列\r\n\r\n @throws UnsupportedOperationException 如果此 UUID 的 version 不为 1\r\n"}, {"name": "node", "paramTypes": [], "doc": "\n 与此 UUID 相关的节点值。\r\n\r\n <p>\r\n 48 位的节点值根据此 UUID 的 node 字段构造。此字段旨在用于保存机器的 IEEE 802 地址，该地址用于生成此 UUID 以保证空间唯一性。\r\n <p>\r\n 节点值仅在基于时间的 UUID（其 version 类型为 1）中才有意义。<br>\r\n 如果此 UUID 不是基于时间的 UUID，则此方法抛出 UnsupportedOperationException。\r\n\r\n @return 此 {@code UUID} 的节点值\r\n\r\n @throws UnsupportedOperationException 如果此 UUID 的 version 不为 1\r\n"}, {"name": "toString", "paramTypes": [], "doc": "\n 返回此{@code UUID} 的字符串表现形式。\r\n\r\n <p>\r\n UUID 的字符串表示形式由此 BNF 描述：\r\n\r\n <pre>\r\n {@code\r\n UUID                   = <time_low>-<time_mid>-<time_high_and_version>-<variant_and_sequence>-<node>\r\n time_low               = 4*<hexOctet>\r\n time_mid               = 2*<hexOctet>\r\n time_high_and_version  = 2*<hexOctet>\r\n variant_and_sequence   = 2*<hexOctet>\r\n node                   = 6*<hexOctet>\r\n hexOctet               = <hexDigit><hexDigit>\r\n hexDigit               = [0-9a-fA-F]\r\n }\r\n </pre>\r\n\r\n </blockquote>\r\n\r\n @return 此{@code UUID} 的字符串表现形式\r\n @see #toString(boolean)\r\n"}, {"name": "toString", "paramTypes": ["boolean"], "doc": "\n 返回此{@code UUID} 的字符串表现形式。\r\n\r\n <p>\r\n UUID 的字符串表示形式由此 BNF 描述：\r\n\r\n <pre>\r\n {@code\r\n UUID                   = <time_low>-<time_mid>-<time_high_and_version>-<variant_and_sequence>-<node>\r\n time_low               = 4*<hexOctet>\r\n time_mid               = 2*<hexOctet>\r\n time_high_and_version  = 2*<hexOctet>\r\n variant_and_sequence   = 2*<hexOctet>\r\n node                   = 6*<hexOctet>\r\n hexOctet               = <hexDigit><hexDigit>\r\n hexDigit               = [0-9a-fA-F]\r\n }\r\n </pre>\r\n\r\n </blockquote>\r\n\r\n @param isSimple 是否简单模式，简单模式为不带'-'的UUID字符串\r\n @return 此{@code UUID} 的字符串表现形式\r\n"}, {"name": "hashCode", "paramTypes": [], "doc": "\n 返回此 UUID 的哈希码。\r\n\r\n @return UUID 的哈希码值。\r\n"}, {"name": "equals", "paramTypes": ["java.lang.Object"], "doc": "\n 将此对象与指定对象比较。\r\n <p>\r\n 当且仅当参数不为 {@code null}、而是一个 UUID 对象、具有与此 UUID 相同的 varriant、包含相同的值（每一位均相同）时，结果才为 {@code true}。\r\n\r\n @param obj 要与之比较的对象\r\n\r\n @return 如果对象相同，则返回 {@code true}；否则返回 {@code false}\r\n"}, {"name": "compareTo", "paramTypes": ["org.dromara.business.utils.uuid.UUID"], "doc": "\n 将此 UUID 与指定的 UUID 比较。\r\n\r\n <p>\r\n 如果两个 UUID 不同，且第一个 UUID 的最高有效字段大于第二个 UUID 的对应字段，则第一个 UUID 大于第二个 UUID。\r\n\r\n @param val 与此 UUID 比较的 UUID\r\n\r\n @return 在此 UUID 小于、等于或大于 val 时，分别返回 -1、0 或 1。\r\n\r\n"}, {"name": "digits", "paramTypes": ["long", "int"], "doc": "\n 返回指定数字对应的hex值\r\n\r\n @param val 值\r\n @param digits 位\r\n @return 值\r\n"}, {"name": "checkTimeBase", "paramTypes": [], "doc": "\n 检查是否为time-based版本UUID\r\n"}, {"name": "getSecureRandom", "paramTypes": [], "doc": "\n 获取{@link SecureRandom}，类提供加密的强随机数生成器 (RNG)\r\n\r\n @return {@link SecureRandom}\r\n"}, {"name": "getRandom", "paramTypes": [], "doc": "\n 获取随机数生成器对象<br>\r\n ThreadLocalRandom是JDK 7之后提供并发产生随机数，能够解决多个线程发生的竞争争夺。\r\n\r\n @return {@link ThreadLocalRandom}\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["byte[]"], "doc": "\n 私有构造\r\n\r\n @param data 数据\r\n"}, {"name": "<init>", "paramTypes": ["long", "long"], "doc": "\n 使用指定的数据构造新的 UUID。\r\n\r\n @param mostSigBits 用于 {@code UUID} 的最高有效 64 位\r\n @param leastSigBits 用于 {@code UUID} 的最低有效 64 位\r\n"}]}
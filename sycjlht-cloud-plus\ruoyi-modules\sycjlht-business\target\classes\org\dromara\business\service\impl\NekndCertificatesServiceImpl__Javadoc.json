{"doc": "\n 证书发布Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-06-27\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndCertificatesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询证书发布\r\n\r\n @param id 证书发布主键\r\n @return 证书发布\r\n"}, {"name": "selectNekndCertificatesList", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": "\n 查询证书发布列表\r\n\r\n @param nekndCertificates 证书发布\r\n @return 证书发布\r\n"}, {"name": "selectPageNekndCertificatesList", "paramTypes": ["org.dromara.business.domain.NekndCertificates", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询证书发布列表\r\n\r\n @param nekndCertificates 证书发布\r\n @param pageQuery 分页参数\r\n @return 证书发布分页集合\r\n"}, {"name": "insertNekndCertificates", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": "\n 新增证书发布\r\n\r\n @param nekndCertificates 证书发布\r\n @return 结果\r\n"}, {"name": "updateNekndCertificates", "paramTypes": ["org.dromara.business.domain.NekndCertificates"], "doc": "\n 修改证书发布\r\n\r\n @param nekndCertificates 证书发布\r\n @return 结果\r\n"}, {"name": "deleteNekndCertificatesByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除证书发布\r\n\r\n @param ids 需要删除的证书发布主键\r\n @return 结果\r\n"}, {"name": "deleteNekndCertificatesById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除证书发布信息\r\n\r\n @param id 证书发布主键\r\n @return 结果\r\n"}], "constructors": []}
{"doc": "\n 科研成果Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-12\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndResearchAchievementById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询科研成果\r\n\r\n @param id 科研成果主键\r\n @return 科研成果\r\n"}, {"name": "selectNekndResearchAchievementList", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 查询科研成果列表\r\n\r\n @param nekndResearchAchievement 科研成果\r\n @return 科研成果集合\r\n"}, {"name": "insertNekndResearchAchievement", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 新增科研成果\r\n\r\n @param nekndResearchAchievement 科研成果\r\n @return 结果\r\n"}, {"name": "updateNekndResearchAchievement", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 修改科研成果\r\n\r\n @param nekndResearchAchievement 科研成果\r\n @return 结果\r\n"}, {"name": "deleteNekndResearchAchievementById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除科研成果\r\n\r\n @param id 科研成果主键\r\n @return 结果\r\n"}, {"name": "deleteNekndResearchAchievementByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除科研成果\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}, {"name": "getRecommendOutcomes", "paramTypes": [], "doc": "\n 科技成果推荐成果\r\n @return\r\n"}, {"name": "getSift", "paramTypes": ["org.dromara.business.domain.NekndResearchAchievement"], "doc": "\n 按关键字筛选\r\n @param achievement\r\n @return\r\n"}], "constructors": []}
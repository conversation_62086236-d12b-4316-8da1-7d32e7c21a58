# Tomcat
server:
  port: 9206

# Spring
spring:
  application:
    # 应用名称
    name: sycjlht-business
  profiles:
    # 环境配置
    active: dev

--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: 123.60.174.83:8848
      username: nacos
      password: SANyun666
      discovery:
        # 注册组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: DEFAULT_GROUP
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml

--- # dubbo 配置
dubbo:
  consumer:
    # 增加超时时间
    timeout: 10000
    # 允许检查失败
    check: false
  registry:
    # 确保使用测试环境的注册中心地址
    address: nacos://${spring.cloud.nacos.server-addr}

--- # 历史图片配置
legacy:
  image:
    # 历史路径前缀
    prefix: /profile/upload

--- # OSS配置
oss:
  # OSS域名
  domain: https://sanyun-oss-1306151097.cos.ap-shanghai.myqcloud.com
  # OSS路径前缀（根据您的实际OSS路径结构）
  path-prefix: /bt_backup/path/home/<USER>/uploadPath

--- # 邮箱配置
mail:
  host: smtp.163.com
  port: 25
  username: <EMAIL>
  password: ZMQkfdJ9T5KBc4DQ
  properties:
    mail:
      smtp:
        auth: true
        starttls:
          enable: true



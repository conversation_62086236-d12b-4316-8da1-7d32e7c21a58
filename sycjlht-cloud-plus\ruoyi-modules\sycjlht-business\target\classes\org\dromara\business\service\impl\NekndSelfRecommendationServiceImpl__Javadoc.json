{"doc": "\n 自荐信息Service业务层处理\r\n\r\n <AUTHOR>\r\n @date 2024-05-17\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSelfRecommendationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询自荐信息\r\n\r\n @param id 自荐信息主键\r\n @return 自荐信息\r\n"}, {"name": "selectNekndSelfRecommendationList", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": "\n 查询自荐信息列表\r\n\r\n @param nekndSelfRecommendation 自荐信息\r\n @return 自荐信息\r\n"}, {"name": "insertNekndSelfRecommendation", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": "\n 新增自荐信息\r\n\r\n @param nekndSelfRecommendation 自荐信息\r\n @return 结果\r\n"}, {"name": "updateNekndSelfRecommendation", "paramTypes": ["org.dromara.business.domain.NekndSelfRecommendation"], "doc": "\n 修改自荐信息\r\n\r\n @param nekndSelfRecommendation 自荐信息\r\n @return 结果\r\n"}, {"name": "deleteNekndSelfRecommendationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除自荐信息\r\n\r\n @param ids 需要删除的自荐信息主键\r\n @return 结果\r\n"}, {"name": "deleteNekndSelfRecommendationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除自荐信息信息\r\n\r\n @param id 自荐信息主键\r\n @return 结果\r\n"}, {"name": "selectListByrequirementId", "paramTypes": ["java.lang.Integer"], "doc": "\n 根据项目需求id查询自荐信息列表\r\n @param requirementId\r\n @return\r\n"}], "constructors": []}
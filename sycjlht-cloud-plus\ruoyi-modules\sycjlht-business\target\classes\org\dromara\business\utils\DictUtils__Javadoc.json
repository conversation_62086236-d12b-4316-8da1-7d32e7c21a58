{"doc": "\n 字典工具类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "SEPARATOR", "doc": "\n 分隔符\r\n"}], "enumConstants": [], "methods": [{"name": "setDict<PERSON>ache", "paramTypes": ["java.lang.String", "java.util.List"], "doc": "\n 设置字典缓存\r\n\r\n @param key 参数键\r\n @param dictDatas 字典数据列表\r\n"}, {"name": "getDictCache", "paramTypes": ["java.lang.String"], "doc": "\n 获取字典缓存\r\n\r\n @param key 参数键\r\n @return dictDatas 字典数据列表\r\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典值获取字典标签\r\n\r\n @param dictType 字典类型\r\n @param dictValue 字典值\r\n @return 字典标签\r\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典标签获取字典值\r\n\r\n @param dictType 字典类型\r\n @param dictLabel 字典标签\r\n @return 字典值\r\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典值获取字典标签\r\n\r\n @param dictType 字典类型\r\n @param dictValue 字典值\r\n @param separator 分隔符\r\n @return 字典标签\r\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典标签获取字典值\r\n\r\n @param dictType 字典类型\r\n @param dictLabel 字典标签\r\n @param separator 分隔符\r\n @return 字典值\r\n"}, {"name": "removeDictCache", "paramTypes": ["java.lang.String"], "doc": "\n 删除指定字典缓存\r\n\r\n @param key 字典键\r\n"}, {"name": "clearDictCache", "paramTypes": [], "doc": "\n 清空字典缓存\r\n"}, {"name": "get<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 设置cache key\r\n\r\n @param configKey 参数键\r\n @return 缓存键key\r\n"}], "constructors": []}
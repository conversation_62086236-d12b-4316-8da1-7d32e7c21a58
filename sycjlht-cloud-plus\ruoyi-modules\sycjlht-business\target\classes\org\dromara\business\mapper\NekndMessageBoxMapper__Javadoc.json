{"doc": "\n 留言箱Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-06-02\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询留言箱\r\n\r\n @param id 留言箱主键\r\n @return 留言箱\r\n"}, {"name": "selectNekndMessageBoxList", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 查询留言箱列表\r\n\r\n @param nekndMessageBox 留言箱\r\n @return 留言箱集合\r\n"}, {"name": "insertNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 新增留言箱\r\n\r\n @param nekndMessageBox 留言箱\r\n @return 结果\r\n"}, {"name": "updateNekndMessageBox", "paramTypes": ["org.dromara.business.domain.NekndMessageBox"], "doc": "\n 修改留言箱\r\n\r\n @param nekndMessageBox 留言箱\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageBoxById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除留言箱\r\n\r\n @param id 留言箱主键\r\n @return 结果\r\n"}, {"name": "deleteNekndMessageBoxByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除留言箱\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
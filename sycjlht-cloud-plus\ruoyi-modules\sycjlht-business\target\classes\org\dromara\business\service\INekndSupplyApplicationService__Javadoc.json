{"doc": "\n 企业数字化应用Service接口\r\n\r\n <AUTHOR>\r\n @date 2024-05-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndSupplyApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询企业数字化应用\r\n\r\n @param id 企业数字化应用主键\r\n @return 企业数字化应用\r\n"}, {"name": "selectNekndSupplyApplicationList", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": "\n 查询企业数字化应用列表\r\n\r\n @param nekndSupplyApplication 企业数字化应用\r\n @return 企业数字化应用集合\r\n"}, {"name": "insertNekndSupplyApplication", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": "\n 新增企业数字化应用\r\n\r\n @param nekndSupplyApplication 企业数字化应用\r\n @return 结果\r\n"}, {"name": "updateNekndSupplyApplication", "paramTypes": ["org.dromara.business.domain.NekndSupplyApplication"], "doc": "\n 修改企业数字化应用\r\n\r\n @param nekndSupplyApplication 企业数字化应用\r\n @return 结果\r\n"}, {"name": "deleteNekndSupplyApplicationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除企业数字化应用\r\n\r\n @param ids 需要删除的企业数字化应用主键集合\r\n @return 结果\r\n"}, {"name": "deleteNekndSupplyApplicationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除企业数字化应用信息\r\n\r\n @param id 企业数字化应用主键\r\n @return 结果\r\n"}], "constructors": []}
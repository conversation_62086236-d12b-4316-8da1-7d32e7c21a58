{"doc": " 政策新闻信息Mapper接口\n\n <AUTHOR>\n @date 2025-01-15\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPreviousNews", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 查询上一条新闻\n\n @param newsType 新闻类型\n @param currentId 当前新闻ID\n @return 上一条新闻\n"}, {"name": "selectNextNews", "paramTypes": ["java.lang.String", "java.lang.Long"], "doc": " 查询下一条新闻\n\n @param newsType 新闻类型\n @param currentId 当前新闻ID\n @return 下一条新闻\n"}], "constructors": []}
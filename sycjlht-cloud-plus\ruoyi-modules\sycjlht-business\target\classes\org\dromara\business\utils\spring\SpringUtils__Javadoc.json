{"doc": "\n spring工具类 方便在非spring管理环境中获取bean\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "beanFactory", "doc": "Spring应用上下文环境 "}], "enumConstants": [], "methods": [{"name": "get<PERSON>ean", "paramTypes": ["java.lang.String"], "doc": "\n 获取对象\r\n\r\n @param name\r\n @return Object 一个以所给名字注册的bean的实例\r\n @throws BeansException\r\n\r\n"}, {"name": "get<PERSON>ean", "paramTypes": ["java.lang.Class"], "doc": "\n 获取类型为requiredType的对象\r\n\r\n @param clz\r\n @return\r\n @throws BeansException\r\n\r\n"}, {"name": "containsBean", "paramTypes": ["java.lang.String"], "doc": "\n 如果BeanFactory包含一个与所给名称匹配的bean定义，则返回true\r\n\r\n @param name\r\n @return boolean\r\n"}, {"name": "isSingleton", "paramTypes": ["java.lang.String"], "doc": "\n 判断以给定名字注册的bean定义是一个singleton还是一个prototype。 如果与给定名字相应的bean定义没有被找到，将会抛出一个异常（NoSuchBeanDefinitionException）\r\n\r\n @param name\r\n @return boolean\r\n @throws NoSuchBeanDefinitionException\r\n\r\n"}, {"name": "getType", "paramTypes": ["java.lang.String"], "doc": "\n @param name\r\n @return Class 注册对象的类型\r\n @throws NoSuchBeanDefinitionException\r\n\r\n"}, {"name": "getAliases", "paramTypes": ["java.lang.String"], "doc": "\n 如果给定的bean名字在bean定义中有别名，则返回这些别名\r\n\r\n @param name\r\n @return\r\n @throws NoSuchBeanDefinitionException\r\n\r\n"}, {"name": "getAopProxy", "paramTypes": ["java.lang.Object"], "doc": "\n 获取aop代理对象\r\n\r\n @param invoker\r\n @return\r\n"}, {"name": "getActiveProfiles", "paramTypes": [], "doc": "\n 获取当前的环境配置，无配置返回null\r\n\r\n @return 当前的环境配置\r\n"}, {"name": "getActiveProfile", "paramTypes": [], "doc": "\n 获取当前的环境配置，当有多个环境配置时，只获取第一个\r\n\r\n @return 当前的环境配置\r\n"}, {"name": "getRequiredProperty", "paramTypes": ["java.lang.String"], "doc": "\n 获取配置文件中的值\r\n\r\n @param key 配置文件的key\r\n @return 当前的配置文件的值\r\n\r\n"}], "constructors": []}
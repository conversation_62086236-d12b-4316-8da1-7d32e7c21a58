{"doc": "\n <AUTHOR> 序列生成类\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getId", "paramTypes": [], "doc": "\n 获取通用序列号\r\n\r\n @return 序列值\r\n"}, {"name": "getId", "paramTypes": ["java.lang.String"], "doc": "\n 默认16位序列号 yyMMddHHmmss + 一位机器标识 + 3长度循环递增字符串\r\n\r\n @return 序列值\r\n"}, {"name": "getId", "paramTypes": ["java.util.concurrent.atomic.AtomicInteger", "int"], "doc": "\n 通用接口序列号 yyMMddHHmmss + 一位机器标识 + length长度循环递增字符串\r\n\r\n @param atomicInt 序列数\r\n @param length 数值长度\r\n @return 序列值\r\n"}, {"name": "getSeq", "paramTypes": ["java.util.concurrent.atomic.AtomicInteger", "int"], "doc": "\n 序列循环递增字符串[1, 10 的 (length)幂次方), 用0左补齐length位数\r\n\r\n @return 序列值\r\n"}], "constructors": []}
{"doc": "\n 师资评价Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-12-09\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndTeacherEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询师资评价\r\n\r\n @param id 师资评价主键\r\n @return 师资评价\r\n"}, {"name": "selectNekndTeacherEvaluationList", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": "\n 查询师资评价列表\r\n\r\n @param nekndTeacherEvaluation 师资评价\r\n @return 师资评价集合\r\n"}, {"name": "insertNekndTeacherEvaluation", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": "\n 新增师资评价\r\n\r\n @param nekndTeacherEvaluation 师资评价\r\n @return 结果\r\n"}, {"name": "updateNekndTeacherEvaluation", "paramTypes": ["org.dromara.business.domain.NekndTeacherEvaluation"], "doc": "\n 修改师资评价\r\n\r\n @param nekndTeacherEvaluation 师资评价\r\n @return 结果\r\n"}, {"name": "deleteNekndTeacherEvaluationById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除师资评价\r\n\r\n @param id 师资评价主键\r\n @return 结果\r\n"}, {"name": "deleteNekndTeacherEvaluationByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除师资评价\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndCoursesCollectionMapper">
    
    <resultMap type="NekndCoursesCollection" id="NekndCoursesCollectionResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="coursesId"    column="courses_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNekndCoursesCollectionVo">
        select id, user_id, courses_id, create_time from neknd_courses_collection
    </sql>

    <select id="selectNekndCoursesCollectionList" parameterType="NekndCoursesCollection" resultMap="NekndCoursesCollectionResult">
        <include refid="selectNekndCoursesCollectionVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="coursesId != null "> and courses_id = #{coursesId}</if>
        </where>
    </select>
    
    <select id="selectNekndCoursesCollectionById" parameterType="Integer" resultMap="NekndCoursesCollectionResult">
        <include refid="selectNekndCoursesCollectionVo"/>
        where id = #{id}
    </select>
    <select id="selectFavoriteCountByCourseId" resultType="java.lang.Long">
        select count(1) from neknd_courses_collection where courses_id = #{courseId}
    </select>

    <insert id="insertNekndCoursesCollection" parameterType="NekndCoursesCollection" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_courses_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="coursesId != null">courses_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="coursesId != null">#{coursesId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateNekndCoursesCollection" parameterType="NekndCoursesCollection">
        update neknd_courses_collection
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="coursesId != null">courses_id = #{coursesId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndCoursesCollectionById" parameterType="Integer">
        delete from neknd_courses_collection where id = #{id}
    </delete>

    <delete id="deleteNekndCoursesCollectionByIds" parameterType="String">
        delete from neknd_courses_collection where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
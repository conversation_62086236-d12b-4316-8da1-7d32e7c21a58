{"doc": "\n 反射工具类. 提供调用getter/setter方法, 访问私有变量, 调用私有方法, 获取泛型类型Class, 被AOP过的真实类等工具函数.\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "invokeGetter", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": "\n 调用Getter方法.\r\n 支持多级，如：对象名.对象名.方法\r\n"}, {"name": "invokeSetter", "paramTypes": ["java.lang.Object", "java.lang.String", "java.lang.Object"], "doc": "\n 调用Setter方法, 仅匹配方法名。\r\n 支持多级，如：对象名.对象名.方法\r\n"}, {"name": "getFieldValue", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": "\n 直接读取对象属性值, 无视private/protected修饰符, 不经过getter函数.\r\n"}, {"name": "setFieldValue", "paramTypes": ["java.lang.Object", "java.lang.String", "java.lang.Object"], "doc": "\n 直接设置对象属性值, 无视private/protected修饰符, 不经过setter函数.\r\n"}, {"name": "invoke<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Object", "java.lang.String", "java.lang.Class[]", "java.lang.Object[]"], "doc": "\n 直接调用对象方法, 无视private/protected修饰符.\r\n 用于一次性调用的情况，否则应使用getAccessibleMethod()函数获得Method后反复调用.\r\n 同时匹配方法名+参数类型，\r\n"}, {"name": "invokeMethodByName", "paramTypes": ["java.lang.Object", "java.lang.String", "java.lang.Object[]"], "doc": "\n 直接调用对象方法, 无视private/protected修饰符，\r\n 用于一次性调用的情况，否则应使用getAccessibleMethodByName()函数获得Method后反复调用.\r\n 只匹配函数名，如果有多个同名函数调用第一个。\r\n"}, {"name": "getAccessibleField", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": "\n 循环向上转型, 获取对象的DeclaredField, 并强制设置为可访问.\r\n 如向上转型到Object仍无法找到, 返回null.\r\n"}, {"name": "getAccessibleMethod", "paramTypes": ["java.lang.Object", "java.lang.String", "java.lang.Class[]"], "doc": "\n 循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.\r\n 如向上转型到Object仍无法找到, 返回null.\r\n 匹配函数名+参数类型。\r\n 用于方法需要被多次调用的情况. 先使用本函数先取得Method,然后调用Method.invoke(Object obj, Object... args)\r\n"}, {"name": "getAccessibleMethodByName", "paramTypes": ["java.lang.Object", "java.lang.String", "int"], "doc": "\n 循环向上转型, 获取对象的DeclaredMethod,并强制设置为可访问.\r\n 如向上转型到Object仍无法找到, 返回null.\r\n 只匹配函数名。\r\n 用于方法需要被多次调用的情况. 先使用本函数先取得Method,然后调用Method.invoke(Object obj, Object... args)\r\n"}, {"name": "makeAccessible", "paramTypes": ["java.lang.reflect.Method"], "doc": "\n 改变private/protected的方法为public，尽量不调用实际改动的语句，避免JDK的SecurityManager抱怨。\r\n"}, {"name": "makeAccessible", "paramTypes": ["java.lang.reflect.Field"], "doc": "\n 改变private/protected的成员变量为public，尽量不调用实际改动的语句，避免JDK的SecurityManager抱怨。\r\n"}, {"name": "getClassGenricType", "paramTypes": ["java.lang.Class"], "doc": "\n 通过反射, 获得Class定义中声明的泛型参数的类型, 注意泛型必须定义在父类处\r\n 如无法找到, 返回Object.class.\r\n"}, {"name": "getClassGenricType", "paramTypes": ["java.lang.Class", "int"], "doc": "\n 通过反射, 获得Class定义中声明的父类的泛型参数的类型.\r\n 如无法找到, 返回Object.class.\r\n"}, {"name": "convertReflectionExceptionToUnchecked", "paramTypes": ["java.lang.String", "java.lang.Exception"], "doc": "\n 将反射时的checked exception转换为unchecked exception.\r\n"}], "constructors": []}
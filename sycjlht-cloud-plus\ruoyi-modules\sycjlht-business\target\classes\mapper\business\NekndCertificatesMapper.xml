<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndCertificatesMapper">
    
    <resultMap type="NekndCertificates" id="NekndCertificatesResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="createId"    column="create_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createName"    column="create_name"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="departmentType"    column="department_type"    />
    </resultMap>

    <sql id="selectNekndCertificatesVo">
        select id, cover_uri, create_id, title, content, type, create_time, update_time, remark, del_flag, create_name,review_status,department_type from neknd_certificates
    </sql>

    <select id="selectNekndCertificatesList" parameterType="NekndCertificates" resultMap="NekndCertificatesResult">
        <include refid="selectNekndCertificatesVo"/>
        <where>
            del_flag = 0
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createId != null  and createId != ''"> and create_id = #{createId}</if>
            <if test="reviewStatus != null  and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
            <if test="departmentType != null  and departmentType != ''"> and department_type = #{departmentType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndCertificatesById" parameterType="Integer" resultMap="NekndCertificatesResult">
        <include refid="selectNekndCertificatesVo"/>
        where id = #{id}
    </select>
    <select id="getCountCertification" resultType="java.lang.Integer">
        select count(1) from neknd_certificates where del_flag = 0 and review_status = 1
    </select>

    <insert id="insertNekndCertificates" parameterType="NekndCertificates">
        insert into neknd_certificates
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="coverUri != null">cover_uri,</if>
            <if test="createId != null">create_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createName != null">create_name,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="departmentType != null">department_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="coverUri != null">#{coverUri},</if>
            <if test="createId != null">#{createId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createName != null">#{createName},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="departmentType != null">#{departmentType},</if>
         </trim>
    </insert>

    <update id="updateNekndCertificates" parameterType="NekndCertificates">
        update neknd_certificates
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="departmentType != null">department_type = #{departmentType},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateAuditing" >
        update neknd_certificates set review_status = #{reviewStatus} where id=#{id}
    </update>

    <delete id="deleteNekndCertificatesById" parameterType="Integer">
        delete from neknd_certificates where id = #{id}
    </delete>

    <delete id="deleteNekndCertificatesByIds" parameterType="String">
        delete from neknd_certificates where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <resultMap id="certificatesMap" type="HashMap">
        <result property="name" column="title" />
        <result property="value" column="count" />
    </resultMap>
    <select id="getPopularCertificates" resultMap="certificatesMap">
        select title,count(title) as count from neknd_certificates where del_flag =0 GROUP BY title order by count desc limit 8;
    </select>
</mapper>
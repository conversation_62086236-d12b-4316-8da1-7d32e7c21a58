<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndTechnicalStandardProjectMapper">
    
    <resultMap type="NekndTechnicalStandardProject" id="NekndTechnicalStandardProjectResult">
        <result property="id"    column="id"    />
        <result property="coverUri"    column="cover_uri"    />
        <result property="newsTitle"    column="news_title"    />
        <result property="newsType"    column="news_type"    />
        <result property="newsContent"    column="news_content"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="sourceTitle"    column="source_title"    />
        <result property="provincialId"    column="provincial_id"    />
        <result property="provincialName"    column="provincial_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
    </resultMap>

    <sql id="selectNekndTechnicalStandardProjectVo">
        select id, cover_uri, news_title, news_type, news_content, status, del_flag, create_by, create_time, update_by, update_time, remark, source_title, provincial_id, provincial_name,
               city_id, city_name from neknd_technical_standard_project
    </sql>

    <select id="selectNekndTechnicalStandardProjectList" parameterType="NekndTechnicalStandardProject" resultMap="NekndTechnicalStandardProjectResult">
        <include refid="selectNekndTechnicalStandardProjectVo"/>
        <where>  
            <if test="coverUri != null  and coverUri != ''"> and cover_uri = #{coverUri}</if>
            <if test="newsTitle != null  and newsTitle != ''"> and news_title = #{newsTitle}</if>
            <if test="newsType != null  and newsType != ''"> and news_type = #{newsType}</if>
            <if test="newsContent != null  and newsContent != ''"> and news_content = #{newsContent}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="delFlag != null  and delFlag != ''"> and del_flag = #{delFlag}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
            <if test="sourceTitle != null  and sourceTitle != ''"> and source_title = #{sourceTitle}</if>
            <if test="provincialId != null  and provincialId != ''"> and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''"> and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name = #{cityName}</if>
        </where>
    </select>
    
    <select id="selectNekndTechnicalStandardProjectById" parameterType="Integer" resultMap="NekndTechnicalStandardProjectResult">
        <include refid="selectNekndTechnicalStandardProjectVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndTechnicalStandardProject" parameterType="NekndTechnicalStandardProject" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_technical_standard_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">cover_uri,</if>
            <if test="newsTitle != null">news_title,</if>
            <if test="newsType != null">news_type,</if>
            <if test="newsContent != null">news_content,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="sourceTitle != null">source_title,</if>
            <if test="provincialId != null">provincial_id,</if>
            <if test="provincialName != null">provincial_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="coverUri != null">#{coverUri},</if>
            <if test="newsTitle != null">#{newsTitle},</if>
            <if test="newsType != null">#{newsType},</if>
            <if test="newsContent != null">#{newsContent},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="sourceTitle != null">#{sourceTitle},</if>
            <if test="provincialId != null">#{provincialId},</if>
            <if test="provincialName != null">#{provincialName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
         </trim>
    </insert>

    <update id="updateNekndTechnicalStandardProject" parameterType="NekndTechnicalStandardProject">
        update neknd_technical_standard_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="coverUri != null">cover_uri = #{coverUri},</if>
            <if test="newsTitle != null">news_title = #{newsTitle},</if>
            <if test="newsType != null">news_type = #{newsType},</if>
            <if test="newsContent != null">news_content = #{newsContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="sourceTitle != null">source_title = #{sourceTitle},</if>
            <if test="provincialId != null">provincial_id = #{provincialId},</if>
            <if test="provincialName != null">provincial_name = #{provincialName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndTechnicalStandardProjectById" parameterType="Integer">
        delete from neknd_technical_standard_project where id = #{id}
    </delete>

    <delete id="deleteNekndTechnicalStandardProjectByIds" parameterType="String">
        delete from neknd_technical_standard_project where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
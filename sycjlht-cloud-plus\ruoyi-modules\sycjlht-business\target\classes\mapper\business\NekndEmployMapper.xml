<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndEmployMapper">
    
    <resultMap type="NekndEmploy" id="NekndEmployResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="status"    column="status"    />
        <result property="jobExperience"    column="job_experience"    />
        <result property="jobEducation"    column="job_education"    />
        <result property="jobNum"    column="job_num"    />
        <result property="jobSalary"    column="job_salary"    />
        <result property="jobFlag"    column="job_flag"    />
        <result property="jobName"    column="job_name"    />
        <result property="jobType"    column="job_type"    />
        <result property="provincialId"    column="provincial_id"    />
        <result property="provincialName"    column="provincial_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="aiQuestionJson"    column="ai_question_json"    />
        <result property="aiStatus"    column="ai_status"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="jobRequirements"    column="job_requirements"    />
        <result property="priceLow"    column="price_low"    />
        <result property="priceHigh"    column="price_high"    />
    </resultMap>

    <sql id="selectNekndEmployVo">
        select id, del_flag, create_by, create_time, update_by, update_time, remark, dept_id, status, job_experience, job_education, job_num, job_salary, job_flag, job_name, job_type, provincial_id, provincial_name, city_id, city_name, ai_status, ai_question_json, review_status, is_top, job_requirements, price_low, price_high from neknd_employ
    </sql>

    <select id="selectNekndEmployList" parameterType="NekndEmploy" resultMap="NekndEmployResult">
        <include refid="selectNekndEmployVo"/>
        <where>
            del_flag = 0
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="jobExperience != null  and jobExperience != ''"> and job_experience = #{jobExperience}</if>
            <if test="jobEducation != null  and jobEducation != ''"> and job_education = #{jobEducation}</if>
<!--            <if test="jobNum != null  and jobNum != ''"> and job_num = #{jobNum}</if>-->
            <if test="internshipJobNum != null  and internshipJobNum != '' and internshipJobNum!= false"> and job_num > 20</if>
            <if test="jobSalary != null  and jobSalary != ''"> and job_salary = #{jobSalary}</if>
            <if test="jobFlag != null  and jobFlag != ''"> and job_flag like concat('%',#{jobFlag},'%')</if>
            <if test="jobName != null  and jobName != ''"> and job_name like concat('%',#{jobName},'%')</if>
            <if test="jobType != null  and jobType != '' and jobType != 0"> and job_type = #{jobType}</if>
            <if test="jobTypes != null ">
                and job_type IN
                <foreach item="item" index="index" collection="jobTypes" open="(" separator="," close=")">
                    #{item}
                </foreach></if>
            <if test="provincialId != null  and provincialId != ''"> and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''"> and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name = #{cityName}</if>
            <if test="aiStatus != null  and aiStatus != ''"> and ai_status = #{aiStatus}</if>
            <if test="aiQuestionJson != null  and aiQuestionJson != ''"> and ai_question_json = #{aiQuestionJson}</if>
            <if test="reviewStatus != null  and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
            <if test="jobRequirements != null  and jobRequirements != ''"> and job_requirements = #{jobRequirements}</if>
            <if test="priceLow != null  and priceLow != ''"> and price_low &gt;= #{priceLow}</if>
            <if test="priceHigh != null  and priceHigh != ''"> and price_high = #{priceHigh}</if>
        </where>
        order by is_top desc , create_time desc
    </select>

    <select id="selectNekndEmployListReview" parameterType="NekndEmploy" resultMap="NekndEmployResult">
        <include refid="selectNekndEmployVo"/>
        <where>
            del_flag = 0
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="jobExperience != null  and jobExperience != ''"> and job_experience = #{jobExperience}</if>
            <if test="jobEducation != null  and jobEducation != ''"> and job_education = #{jobEducation}</if>
<!--            <if test="jobNum != null  and jobNum != ''"> and job_num = #{jobNum}</if>-->
            <if test="internshipJobNum != null  and internshipJobNum != '' and internshipJobNum!= false"> and job_num > 20</if>
            <if test="jobSalary != null  and jobSalary != ''"> and job_salary = #{jobSalary}</if>
            <if test="jobFlag != null  and jobFlag != ''"> and job_flag like concat('%',#{jobFlag},'%')</if>
            <if test="jobName != null  and jobName != ''"> and job_name like concat('%',#{jobName},'%')</if>
            <if test="jobType != null  and jobType != '' and jobType != 0"> and job_type = #{jobType}</if>
            <if test="jobTypes != null ">
                and job_type IN
                <foreach item="item" index="index" collection="jobTypes" open="(" separator="," close=")">
                    #{item}
                </foreach></if>
            <if test="provincialId != null  and provincialId != ''"> and provincial_id = #{provincialId}</if>
            <if test="provincialName != null  and provincialName != ''"> and provincial_name = #{provincialName}</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name = #{cityName}</if>
            <if test="aiStatus != null  and aiStatus != ''"> and ai_status = #{aiStatus}</if>
            <if test="aiQuestionJson != null  and aiQuestionJson != ''"> and ai_question_json = #{aiQuestionJson}</if>
            <if test="reviewStatus != null  and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
            <if test="jobRequirements != null  and jobRequirements != ''"> and job_requirements = #{jobRequirements}</if>
            <if test="priceLow != null  and priceLow != ''"> and price_low &gt;= #{priceLow}</if>
            <if test="priceHigh != null  and priceLow != ''"> and price_high = #{priceHigh}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndEmployById" parameterType="Integer" resultMap="NekndEmployResult">
        <include refid="selectNekndEmployVo"/>
        where id = #{id}
    </select>
    <select id="selectNekndEmployCount" resultType="java.lang.Integer">
        select count(1) from neknd_employ where del_flag = 0 and status=1 and review_status=1
    </select>
    <select id="selectMaxTop" resultType="java.lang.Integer">
        select max(is_top) from neknd_employ where del_flag = 0 and id &lt;&gt; #{id}
    </select>
    <select id="selectJobTypeByUserId" resultType="java.lang.Long">
        select job_type from neknd_employ e LEFT JOIN sys_user u on e.dept_id = u.dept_id where u.del_flag=0 and e.del_flag=0 and e.status=1 and u.user_id=#{userId}
    </select>

    <resultMap id="RecommendedEmployMap" type="java.util.HashMap">
        <result property="userId" jdbcType="INTEGER" column="user_id"/>
        <result property="name" jdbcType="VARCHAR" column="name"/>
        <result property="sex" jdbcType="VARCHAR" column="sex"/>
        <result property="email" jdbcType="VARCHAR"  column="email"/>
        <result property="jobType" jdbcType="VARCHAR"  column="job_type"/>
        <collection property="employList" jdbcType="VARCHAR"  ofType="NekndEmploy" javaType="List">
            <result property="id" jdbcType="INTEGER" column="employ_id" />
            <result property="deptId" jdbcType="INTEGER" column="dept_id"/>
            <result property="jobExperience" jdbcType="VARCHAR" column="job_experience"/>
            <result property="jobEducation" jdbcType="VARCHAR" column="job_education"/>
            <result property="jobNum" jdbcType="INTEGER" column="job_num"/>
            <result property="jobSalary" jdbcType="VARCHAR" column="job_salary"/>
            <result property="jobFlag" jdbcType="VARCHAR" column="job_flag"/>
            <result property="jobName" jdbcType="VARCHAR" column="job_name"/>
            <result property="remark" jdbcType="VARCHAR" column="remark"/>
            <result property="jobRequirements" jdbcType="VARCHAR" column="job_requirements"/>
        </collection>
    </resultMap>
    <select id="selectPersonListRecommendedEmploy" parameterType="Integer" resultMap="RecommendedEmployMap">
        SELECT p.user_id,p.name,p.sex,p.email,p.job_type,e.id employ_id,e.dept_id,e.job_experience,e.job_education
             ,e.job_num,e.job_salary,e.job_flag,e.job_name,e.remark,e.job_requirements
        FROM neknd_person p left join neknd_employ e
        on IFNULL(p.job_type,0) = IFNULL(e.job_type,0)
        where e.del_flag = 0 and p.del_flag=0 and e.status=1
        and e.review_status = 1 and e.job_num &gt; 0
        and p.email IS NOT NULL AND p.email !=''
        and (p.position_status=0 or p.position_status=2) and p.job_type is not null
        <if test="userId!=null">and p.user_id =#{userId} </if>
    </select>

    <insert id="insertNekndEmploy" parameterType="NekndEmploy" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_employ
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="status != null">status,</if>
            <if test="jobExperience != null">job_experience,</if>
            <if test="jobEducation != null">job_education,</if>
            <if test="jobNum != null">job_num,</if>
            <if test="jobSalary != null">job_salary,</if>
            <if test="jobFlag != null">job_flag,</if>
            <if test="jobName != null">job_name,</if>
            <if test="jobType != null">job_type,</if>
            <if test="provincialId != null">provincial_id,</if>
            <if test="provincialName != null">provincial_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="aiStatus != null "> ai_status,</if>
            <if test="aiQuestionJson != null "> ai_question_json,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="jobRequirements != null">job_requirements,</if>
            <if test="priceLow != null">price_low,</if>
            <if test="priceHigh != null">price_high,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="status != null">#{status},</if>
            <if test="jobExperience != null">#{jobExperience},</if>
            <if test="jobEducation != null">#{jobEducation},</if>
            <if test="jobNum != null">#{jobNum},</if>
            <if test="jobSalary != null">#{jobSalary},</if>
            <if test="jobFlag != null">#{jobFlag},</if>
            <if test="jobName != null">#{jobName},</if>
            <if test="jobType != null">#{jobType},</if>
            <if test="provincialId != null">#{provincialId},</if>
            <if test="provincialName != null">#{provincialName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="aiStatus != null "> #{aiStatus},</if>
            <if test="aiQuestionJson != null "> #{aiQuestionJson},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="jobRequirements != null">#{jobRequirements},</if>
            <if test="priceLow != null">#{priceLow},</if>
            <if test="priceHigh != null">#{priceHigh},</if>
         </trim>
    </insert>

    <update id="updateNekndEmploy" parameterType="NekndEmploy">
        update neknd_employ
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="jobExperience != null">job_experience = #{jobExperience},</if>
            <if test="jobEducation != null">job_education = #{jobEducation},</if>
            <if test="jobNum != null">job_num = #{jobNum},</if>
            <if test="jobSalary != null">job_salary = #{jobSalary},</if>
            <if test="jobFlag != null">job_flag = #{jobFlag},</if>
            <if test="jobName != null">job_name = #{jobName},</if>
            <if test="jobType != null">job_type = #{jobType},</if>
            <if test="provincialId != null">provincial_id = #{provincialId},</if>
            <if test="provincialName != null">provincial_name = #{provincialName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="aiStatus != null "> ai_status = #{aiStatus},</if>
            <if test="aiQuestionJson != null "> ai_question_json = #{aiQuestionJson},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="jobRequirements != null">job_requirements = #{jobRequirements},</if>
            <if test="priceLow != null">price_low = #{priceLow},</if>
            <if test="priceHigh != null">price_high = #{priceHigh},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAuditing" >
        update neknd_employ set review_status = #{reviewStatus} where id=#{id}
    </update>

    <delete id="deleteNekndEmployById" parameterType="Integer">
        delete from neknd_employ where id = #{id}
    </delete>

    <delete id="deleteNekndEmployByIds" parameterType="String">
        delete from neknd_employ where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectJobExperienceDictValueByDictLabel" resultType="String">
        SELECT dict_value
        FROM sys_dict_data
        WHERE dict_type = "sys_job_experience"
            AND dict_label= #{dictLabel}
    </select>

    <select id="selectJobEducationDictValueByDictLabel" resultType="String">
        SELECT dict_value
        FROM sys_dict_data
        WHERE dict_type = "sys_job_education"
          AND dict_label= #{dictLabel}
    </select>

    <select id="selectJobSalaryDictValueByDictLabel" resultType="String">
        SELECT dict_value
        FROM sys_dict_data
        WHERE dict_type = "sys_job_salary"
          AND dict_label= #{dictLabel}
    </select>

    <select id="selectJobTypeDictValueByDictLabel" resultType="String">
        SELECT dict_value
        FROM sys_dict_data
        WHERE dict_type = "sys_job_type"
          AND dict_label= #{dictLabel}
    </select>

    <resultMap id="getJobDisplayMap" type="hashmap">
        <result property="expectedPosition" column="expected_position" />
        <result property="count" column="COUNT(expected_position)" />
    </resultMap>
    <select id="getJobDisplay" resultMap="getJobDisplayMap">
        SELECT expected_position, COUNT(expected_position)
        FROM neknd_person
        WHERE del_flag = 0
          AND (expected_position IS NOT NULL AND expected_position != '' AND expected_position != '全部')
        GROUP BY expected_position
        ORDER BY COUNT(expected_position) DESC
            limit 10;
    </select>

    <resultMap id="getRealTimeEmployMap" type="hashmap">
        <result property="time" column="time" />
        <result property="count" column="count" />
        <result property="jobType" column="job_type" />
    </resultMap>
    <select id="getRealTimeEmploy" resultMap="getRealTimeEmployMap">
        select DATE_FORMAT(e.update_time,'%m-%d') as time,if(e.job_type='1',"销售",IF(e.job_type='2',"行政"
            ,IF(e.job_type='3',"人力资源",IF(e.job_type='4',"财会",IF(e.job_type='5',"IT",IF(e.job_type='6',"市场"
                ,IF(e.job_type='7',"金融",IF(e.job_type='8',"商务",IF(e.job_type='9',"教育",IF(e.job_type='10',"编辑"
                    ,IF(e.job_type='11',"设计",IF(e.job_type='12',"媒体",IF(e.job_type='13',"翻译",IF(e.job_type='14',"高级管理"
                        ,IF(e.job_type='15',"建筑",IF(e.job_type='16',"酒店",IF(e.job_type='17',"交通",IF(e.job_type='18',"零售"
                            ,IF(e.job_type='19',"家政服务",IF(e.job_type='20',"汽车",IF(e.job_type='21',"客户服务",IF(e.job_type='22',"服装"
                                ,IF(e.job_type='23',"法务",IF(e.job_type='24',"医疗",IF(e.job_type='25',"生物",IF(e.job_type='26',"生产",IF(e.job_type='27',"质控"
                                    ,IF(e.job_type='28',"工程机械",IF(e.job_type='29',"能源",IF(e.job_type='30',"技工",IF(e.job_type='31',"半导体",IF(e.job_type='32',"通信技术"
                                        ,IF(e.job_type='33',"储备干部",IF(e.job_type='34',"农/林/牧/渔业",IF(e.job_type='35',"环境科学","其他"))))))))))))))))))))))))))))))))))) as job_type
             ,count(e.job_type) as count  from neknd_employ e LEFT JOIN sys_user u on e.create_by=u.user_id where e.del_flag=0 GROUP BY e.job_type ORDER BY e.create_by ASC limit 0,5;
    </select>

    <resultMap id="getHotJobsMap" type="hashmap">
        <result property="expectedPosition" column="expected_position" />
        <result property="count" column="count" />
    </resultMap>
    <select id="getHotJobs" resultMap="getHotJobsMap">
        SELECT expected_position, COUNT(expected_position) as count
        FROM neknd_person
        WHERE del_flag = 0
          AND (expected_position IS NOT NULL AND expected_position != '' AND expected_position != '全部')
        GROUP BY expected_position
        ORDER BY COUNT(expected_position) DESC
            limit 10;
    </select>

    <update id="updateNekndEmployPrice" parameterType="NekndEmploy">
        update neknd_employ
        <trim prefix="SET" suffixOverrides=",">
            <if test="priceLow != null  and priceLow != ''">price_low = #{priceLow},</if>
            <if test="priceHigh != null  and priceHigh != ''">price_high = #{priceHigh},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
{"doc": "\n 活动预告信息Mapper接口\r\n\r\n <AUTHOR>\r\n @date 2024-10-10\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNekndUpcomingEventsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 查询活动预告信息\r\n\r\n @param id 活动预告信息主键\r\n @return 活动预告信息\r\n"}, {"name": "selectNekndUpcomingEventsList", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": "\n 查询活动预告信息列表\r\n\r\n @param nekndUpcomingEvents 活动预告信息\r\n @return 活动预告信息集合\r\n"}, {"name": "insertNekndUpcomingEvents", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": "\n 新增活动预告信息\r\n\r\n @param nekndUpcomingEvents 活动预告信息\r\n @return 结果\r\n"}, {"name": "updateNekndUpcomingEvents", "paramTypes": ["org.dromara.business.domain.NekndUpcomingEvents"], "doc": "\n 修改活动预告信息\r\n\r\n @param nekndUpcomingEvents 活动预告信息\r\n @return 结果\r\n"}, {"name": "deleteNekndUpcomingEventsById", "paramTypes": ["java.lang.Integer"], "doc": "\n 删除活动预告信息\r\n\r\n @param id 活动预告信息主键\r\n @return 结果\r\n"}, {"name": "deleteNekndUpcomingEventsByIds", "paramTypes": ["java.lang.Integer[]"], "doc": "\n 批量删除活动预告信息\r\n\r\n @param ids 需要删除的数据主键集合\r\n @return 结果\r\n"}], "constructors": []}
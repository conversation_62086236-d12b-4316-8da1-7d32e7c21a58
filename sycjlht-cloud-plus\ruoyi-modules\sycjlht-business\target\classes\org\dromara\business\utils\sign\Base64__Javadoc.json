{"doc": "\n Base64工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "encode", "paramTypes": ["byte[]"], "doc": "\n Encodes hex octects into Base64\r\n\r\n @param binaryData Array containing binaryData\r\n @return Encoded Base64 array\r\n"}, {"name": "decode", "paramTypes": ["java.lang.String"], "doc": "\n Decodes Base64 data into octects\r\n\r\n @param encoded string containing Base64 data\r\n @return Array containind decoded data.\r\n"}, {"name": "removeWhiteSpace", "paramTypes": ["char[]"], "doc": "\n remove WhiteSpace from MIME containing encoded Base64 data.\r\n\r\n @param data the byte array of base64 data (with WS)\r\n @return the new length\r\n"}], "constructors": []}
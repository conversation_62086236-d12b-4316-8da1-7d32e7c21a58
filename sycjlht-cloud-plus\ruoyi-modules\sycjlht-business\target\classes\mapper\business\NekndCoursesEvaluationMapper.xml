<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.business.mapper.NekndCoursesEvaluationMapper">
    
    <resultMap type="NekndCoursesEvaluation" id="NekndCoursesEvaluationResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="coursesId"    column="courses_id"    />
        <result property="content"    column="content"    />
        <result property="reviewStatus"    column="review_status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNekndCoursesEvaluationVo">
        select id, user_id, courses_id, content, review_status, create_time from neknd_courses_evaluation
    </sql>

    <select id="selectNekndCoursesEvaluationList" parameterType="NekndCoursesEvaluation" resultMap="NekndCoursesEvaluationResult">
        <include refid="selectNekndCoursesEvaluationVo"/>
        <where>
            <if test="coursesId!=null">courses_id=#{coursesId}</if>
            <if test="userId!=null">user_id=#{userId}</if>
            <if test="reviewStatus != null  and reviewStatus != ''"> and review_status = #{reviewStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectNekndCoursesEvaluationById" parameterType="Integer" resultMap="NekndCoursesEvaluationResult">
        <include refid="selectNekndCoursesEvaluationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNekndCoursesEvaluation" parameterType="NekndCoursesEvaluation" useGeneratedKeys="true" keyProperty="id">
        insert into neknd_courses_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="coursesId != null">courses_id,</if>
            <if test="content != null">content,</if>
            <if test="reviewStatus != null">review_status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="coursesId != null">#{coursesId},</if>
            <if test="content != null">#{content},</if>
            <if test="reviewStatus != null">#{reviewStatus},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateNekndCoursesEvaluation" parameterType="NekndCoursesEvaluation">
        update neknd_courses_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="coursesId != null">courses_id = #{coursesId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="reviewStatus != null">review_status = #{reviewStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="upAuditNews">
        update neknd_courses_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="reviewStatus != null  ">review_status = #{reviewStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNekndCoursesEvaluationById" parameterType="Integer">
        delete from neknd_courses_evaluation where id = #{id}
    </delete>

    <delete id="deleteNekndCoursesEvaluationByIds" parameterType="String">
        delete from neknd_courses_evaluation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
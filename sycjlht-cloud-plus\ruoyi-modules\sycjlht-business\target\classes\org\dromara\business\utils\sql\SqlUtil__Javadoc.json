{"doc": "\n sql操作工具类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "SQL_REGEX", "doc": "\n 定义常用的 sql关键字\r\n"}, {"name": "SQL_PATTERN", "doc": "\n 仅支持字母、数字、下划线、空格、逗号、小数点（支持多个字段排序）\r\n"}, {"name": "ORDER_BY_MAX_LENGTH", "doc": "\n 限制orderBy最大长度\r\n"}], "enumConstants": [], "methods": [{"name": "escapeOrderBySql", "paramTypes": ["java.lang.String"], "doc": "\n 检查字符，防止注入绕过\r\n"}, {"name": "isValidOrderBySql", "paramTypes": ["java.lang.String"], "doc": "\n 验证 order by 语法是否符合规范\r\n"}, {"name": "filterKeyword", "paramTypes": ["java.lang.String"], "doc": "\n SQL关键字检查\r\n"}], "constructors": []}